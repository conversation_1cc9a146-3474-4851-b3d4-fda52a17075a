<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
<head th:replace="@{/admin/head.html}"></head>
  
</head>

<body>
    <div class="content-area" style="height: 100%; padding: 20px;">
        <h4 class="page-title">添加管理员</h4>

        <div>
        <div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">添加管理员</h5>
        </div>
        <div class="card-body">
            <form id="adminForm" method="post">
                <div class="form-group row mb-3">
                    <label for="lname" class="col-sm-3 col-form-label">账号 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="lname" name="lname">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="password" class="col-sm-3 col-form-label">登录密码 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="password" class="form-control" id="password" name="password">
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-9 offset-sm-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-floppy-fill"></i> 保存
                        </button>
                        <a href="/adminList" class="btn btn-secondary ml-2">
                            <i class="bi bi-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
        <script>
        // 文件上传函数
        function uploadFile(input, fieldName) {
            if (input.files && input.files[0]) {
                var formData = new FormData();
                formData.append('file', input.files[0]);
                formData.append('c', fieldName);
                
                $.ajax({
                    url: '/upload_re',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            // 将服务器返回的文件名设置到隐藏字段中
                            $('#' + fieldName + '_hidden').val(response.fileName);
                            CommonUtils.showToast('文件上传成功', 'success');
                        } else {
                            CommonUtils.showToast(response.message || '文件上传失败', 'error');
                        }
                    },
                    error: function() {
                        CommonUtils.showToast('文件上传失败', 'error');
                    }
                });
            }
        }
        
        // 图片上传函数
        function uploadImage(input, fieldName) {
            if (input.files && input.files[0]) {
                var formData = new FormData();
                formData.append('file', input.files[0]);
                formData.append('c', fieldName);
                
                $.ajax({
                    url: '/upload_re',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            // 将服务器返回的文件名设置到隐藏字段中
                            $('#' + fieldName + '_hidden').val(response.fileName);
                            CommonUtils.showToast('图片上传成功', 'success');
                            
                            // 显示预览图片
                            var previewContainer = $('#' + fieldName + '_file').closest('.image-upload-container').find('.current-image');
                            if (previewContainer.length === 0) {
                                $('#' + fieldName + '_file').closest('.image-upload-container').append('<div class="current-image mt-2"><small class="text-muted">当前图片: </small><br><img src="/upload/' + response.fileName + '" alt="预览图片" style="max-width: 150px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 5px;"></div>');
                            } else {
                                previewContainer.find('img').attr('src', '/upload/' + response.fileName);
                            }
                        } else {
                            CommonUtils.showToast(response.message || '图片上传失败', 'error');
                        }
                    },
                    error: function() {
                        CommonUtils.showToast('图片上传失败', 'error');
                    }
                });
            }
        }
        
        // 图片预览函数
        function previewImage(src) {
            var modal = '<div class="modal fade" id="imagePreviewModal" tabindex="-1">' +
                       '<div class="modal-dialog modal-lg modal-dialog-centered">' +
                       '<div class="modal-content">' +
                       '<div class="modal-header">' +
                       '<h5 class="modal-title">图片预览</h5>' +
                       '<button type="button" class="btn-close" data-bs-dismiss="modal"></button>' +
                       '</div>' +
                       '<div class="modal-body text-center">' +
                       '<img src="' + src + '" class="img-fluid" style="max-height: 70vh;">' +
                       '</div>' +
                       '</div></div></div>';
            
            $('body').append(modal);
            var modalInstance = new bootstrap.Modal(document.getElementById('imagePreviewModal'));
            modalInstance.show();
            
            $('#imagePreviewModal').on('hidden.bs.modal', function() {
                $(this).remove();
            });
        }
        
        $(document).ready(function() {
            $('#adminForm').on('submit', function(e) {
                e.preventDefault();
                
                // 获取表单字段值
                var lname = $('#lname').val().trim();
                var password = $('#password').val().trim();
                
                // 非空验证
                if (!lname) {
                    CommonUtils.showToast('请输入账号', 'error');
                    return;
                }
                if (!password) {
                    CommonUtils.showToast('请输入登录密码', 'error');
                    return;
                }
                
                
                // 使用通用表单提交方法
                CommonUtils.submitForm('#adminForm', 
                    '/adminAdd', 
                    function(response) {
                        // 成功回调
                        CommonUtils.showToast('操作成功！', 'success');
                        setTimeout(function() {
                            window.location.href = '/adminList';
                        }, 1500);
                    },
                    function(error) {
                        // 错误回调
                        console.error('提交失败:', error);
                    }
                );
            });
        });
        </script>

        </div>
    </div>

    <!-- Bootstrap JS -->
</body>

</html>