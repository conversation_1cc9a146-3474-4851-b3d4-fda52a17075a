<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
<head th:replace="@{/admin/head.html}"></head>
  
</head>

<body>
    <div class="content-area" style="height: 100%; padding: 20px;">
        <h4 class="page-title">添加用户信息</h4>

        <div>
        <div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">添加用户信息</h5>
        </div>
        <div class="card-body">
            <form id="userinfoForm" method="post">
                <div class="form-group row mb-3">
                    <label for="account" class="col-sm-3 col-form-label">用户账号 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="account" name="account">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="password" class="col-sm-3 col-form-label">登录密码 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="password" class="form-control" id="password" name="password">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="uname" class="col-sm-3 col-form-label">姓名 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="uname" name="uname">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="gender" class="col-sm-3 col-form-label">性别 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="gender" name="gender">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="phone" class="col-sm-3 col-form-label">手机号码 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="phone" name="phone">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="email" class="col-sm-3 col-form-label">电子邮箱</label>
                    <div class="col-sm-9">
                        <input type="email" class="form-control" id="email" name="email">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="photo" class="col-sm-3 col-form-label">照片</label>
                    <div class="col-sm-9">
                        <input type="file" class="form-control-file" id="photo" name="photo" accept="image/*">
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-9 offset-sm-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-floppy-fill"></i> 保存
                        </button>
                        <a href="/userinfoList" class="btn btn-secondary ml-2">
                            <i class="bi bi-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
        <script>
        $(document).ready(function() {
            $('#userinfoForm').on('submit', function(e) {
                e.preventDefault();
                
                // 非空验证
                var account = $('#account').val().trim();
                var password = $('#password').val().trim();
                var uname = $('#uname').val().trim();
                var gender = $('#gender').val().trim();
                var phone = $('#phone').val().trim();
                
                if (!account) {
                    CommonUtils.showToast('请输入用户账号', 'error');
                    return;
                }
                if (!password) {
                    CommonUtils.showToast('请输入登录密码', 'error');
                    return;
                }
                if (!uname) {
                    CommonUtils.showToast('请输入姓名', 'error');
                    return;
                }
                if (!gender) {
                    CommonUtils.showToast('请输入性别', 'error');
                    return;
                }
                if (!phone) {
                    CommonUtils.showToast('请输入手机号码', 'error');
                    return;
                }
                
                // 使用通用表单提交方法
                CommonUtils.submitForm('#userinfoForm', 
                    '/userinfoAdd', 
                    function(response) {
                        // 成功回调
                        CommonUtils.showToast('操作成功！', 'success');
                        setTimeout(function() {
                            window.location.href = '/userinfoList';
                        }, 1500);
                    },
                    function(error) {
                        // 错误回调
                        console.error('提交失败:', error);
                    }
                );
            });
        });
        </script>

        </div>
    </div>

    <!-- Bootstrap JS -->
</body>

</html>