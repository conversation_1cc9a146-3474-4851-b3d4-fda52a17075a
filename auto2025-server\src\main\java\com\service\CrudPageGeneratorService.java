package com.service;

import com.model.Tables;
import com.model.Mores;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * CRUD页面生成服务
 * 负责根据表结构和字段信息生成增删改查页面
 */
@Service
public class CrudPageGeneratorService {

    @Autowired
    private MoresService moresService;

    @Autowired
    private TablesService tablesService;

    /**
     * 生成单个CRUD页面
     */
    public String generateSingleCrudPage(String template, Tables table, String pageType, String pageTitle) {
        return generateSingleCrudPage(template, table, pageType, pageTitle, null);
    }

    /**
     * 生成单个CRUD页面（带表功能配置）
     */
    public String generateSingleCrudPage(String template, Tables table, String pageType, String pageTitle, Map<String, Boolean> tableFunctions) {
        try {
            String tableName = table.getTname();
            String tableComment = table.getTword() != null ? table.getTword() : table.getTname();
            String entityName = toPascalCase(tableName);
            String entityNameLower = toCamelCase(tableName);

            // 获取表的字段信息
            Mores queryMores = new Mores();
            queryMores.setTid(table.getTid());
            List<Mores> fieldsList = moresService.queryMoresList(queryMores, null);

            System.out.println("生成CRUD页面 - 表名: " + tableName + ", 页面类型: " + pageType + ", 字段数量: " + (fieldsList != null ? fieldsList.size() : 0));

            // 替换基础模板变量 - 适配用户的模板格式
            String content = template;

            // 替换栏目名称
            String columnTitle = pageTitle + tableComment;
            content = content.replace("$栏目名称$", columnTitle);

            // 生成页面内容并替换$memo$占位符
            String memoContent = "";
            if (fieldsList != null && !fieldsList.isEmpty()) {
                switch (pageType.toLowerCase()) {
                    case "add":
                        memoContent = generateAddPageContent(fieldsList, tableName, tableComment);
                        break;
                    case "manage":
                        memoContent = generateManagePageContent(fieldsList, tableName, tableComment, tableFunctions);
                        break;
                    case "edit":
                        memoContent = generateEditPageContent(fieldsList, tableName, tableComment);
                        break;
                    case "list":
                        memoContent = generateListPageContent(fieldsList, tableName, tableComment, tableFunctions);
                        break;
                    case "detail":
                        memoContent = generateDetailPageContent(fieldsList, tableName, tableComment);
                        break;
                    case "delete":
                        memoContent = generateDeletePageContent(fieldsList, tableName, tableComment);
                        break;
                    default:
                        memoContent = generateDefaultPageContent(fieldsList, tableName, tableComment);
                        break;
                }
            } else {
                memoContent = "<div class=\"alert alert-warning\">该表没有字段信息</div>";
            }

            content = content.replace("$memo$", memoContent);

            System.out.println("页面内容生成完成，内容长度: " + memoContent.length());

            return content;

        } catch (Exception e) {
            System.err.println("生成单个CRUD页面失败: " + e.getMessage());
            e.printStackTrace();
            return template; // 返回原模板
        }
    }

    /**
     * 生成添加页面内容
     */
    private String generateAddPageContent(List<Mores> fields, String tableName, String tableComment) {
        StringBuilder content = new StringBuilder();
        String entityNameLower = toCamelCase(tableName);

        // 添加页面容器
        content.append("<div class=\"container-fluid\">\n");
        content.append("    <div class=\"card\">\n");
        content.append("        <div class=\"card-header\">\n");
        content.append("            <h5 class=\"card-title mb-0\">添加").append(tableComment).append("</h5>\n");
        content.append("        </div>\n");
        content.append("        <div class=\"card-body\">\n");
        content.append("            <form id=\"").append(entityNameLower).append("Form\" method=\"post\">\n");

        // 生成表单字段
        content.append(generateFormFields(fields, "add"));

        // 添加按钮
        content.append("                <div class=\"form-group row\">\n");
        content.append("                    <div class=\"col-sm-9 offset-sm-3\">\n");
        content.append("                        <button type=\"submit\" class=\"btn btn-primary\">\n");
        content.append("                            <i class=\"bi bi-floppy-fill\"></i> 保存\n");
        content.append("                        </button>\n");
        content.append("                        <a href=\"/").append(entityNameLower).append("List\" class=\"btn btn-secondary ml-2\">\n");
        content.append("                            <i class=\"bi bi-arrow-left\"></i> 返回\n");
        content.append("                        </a>\n");
        content.append("                    </div>\n");
        content.append("                </div>\n");
        content.append("            </form>\n");
        content.append("        </div>\n");
        content.append("    </div>\n");
        content.append("</div>\n");

        // 添加脚本（样式已在head.html中）
        content.append(generateSubmitScript(fields, tableName, "add"));

        return content.toString();
    }

    /**
     * 生成管理页面内容（数据列表页面）
     */
    private String generateManagePageContent(List<Mores> fields, String tableName, String tableComment, Map<String, Boolean> tableFunctions) {
        StringBuilder content = new StringBuilder();
        String entityNameLower = toCamelCase(tableName);

        // 管理页面容器（显示数据列表）
        content.append("<div class=\"container-fluid\">\n");

        // 搜索表单（只有当有字段的mobt为1时才显示）
        if (hasSearchableFields(fields)) {
            content.append("    <div class=\"card mb-3\">\n");
            content.append("        <div class=\"card-body\">\n");
            content.append(generateSearchForm(fields));
            content.append("        </div>\n");
            content.append("    </div>\n");
        }

        // 去掉添加按钮区域

        // 数据表格
        content.append("    <div class=\"card\">\n");
        content.append("        <div class=\"card-header\">\n");
        content.append("            <h5 class=\"card-title mb-0\">").append(tableComment).append("列表</h5>\n");
        content.append("        </div>\n");
        content.append("        <div class=\"card-body\">\n");
        content.append("            <div class=\"table-responsive\">\n");
        content.append("                <table class=\"table table-striped table-hover\">\n");
        content.append("                    <thead class=\"thead-light\">\n");
        content.append("                        <tr>\n");
        content.append(generateTableHeaders(fields));

        // 只有当有操作功能时才显示操作列
        if (tableFunctions != null && (tableFunctions.getOrDefault("backendEdit", false) ||
            tableFunctions.getOrDefault("backendDetail", false) ||
            tableFunctions.getOrDefault("backendDelete", false))) {
            content.append("                            <th>操作</th>\n");
        }

        content.append("                        </tr>\n");
        content.append("                    </thead>\n");
        content.append("                    <tbody>\n");
        content.append("                        <tr th:each=\"item : ${list}\">\n");
        content.append(generateTableColumns(fields));

        // 根据表功能配置生成操作按钮
        if (tableFunctions != null) {
            content.append(generateActionButtons(tableName, tableFunctions, fields));
        }

        content.append("                        </tr>\n");
        content.append("                        <tr th:if=\"${#lists.isEmpty(list)}\">\n");
        content.append("                            <td colspan=\"100%\" class=\"text-center text-muted\">暂无数据</td>\n");
        content.append("                        </tr>\n");
        content.append("                    </tbody>\n");
        content.append("                </table>\n");
        content.append("            \n");
        content.append("            <!-- 分页区域 -->\n");
        content.append("            <nav aria-label=\"分页导航\" th:if=\"${totalPages > 1}\">\n");
        content.append("                <ul class=\"pagination justify-content-center\">\n");
        content.append("                    <li class=\"page-item\" th:classappend=\"${currentPage == 1} ? 'disabled'\">\n");
        content.append("                        <a class=\"page-link\" th:href=\"@{''(page=${currentPage - 1})}\">上一页</a>\n");
        content.append("                    </li>\n");
        content.append("                    <li class=\"page-item\" th:each=\"i : ${#numbers.sequence(1, totalPages)}\" \n");
        content.append("                        th:classappend=\"${i == currentPage} ? 'active'\">\n");
        content.append("                        <a class=\"page-link\" th:href=\"@{''(page=${i})}\" th:text=\"${i}\"></a>\n");
        content.append("                    </li>\n");
        content.append("                    <li class=\"page-item\" th:classappend=\"${currentPage == totalPages} ? 'disabled'\">\n");
        content.append("                        <a class=\"page-link\" th:href=\"@{''(page=${currentPage + 1})}\">下一页</a>\n");
        content.append("                    </li>\n");
        content.append("                </ul>\n");
        content.append("            </nav>\n");
        content.append("        </div>\n");
        content.append("    </div>\n");
        content.append("</div>\n");

        // 添加样式和脚本
        content.append(generateCommonStyles());
        content.append(generatePaginationScript(tableName));

        return content.toString();
    }

    /**
     * 生成编辑页面内容
     */
    private String generateEditPageContent(List<Mores> fields, String tableName, String tableComment) {
        StringBuilder content = new StringBuilder();
        String entityNameLower = toCamelCase(tableName);

        // 编辑页面容器
        content.append("<div class=\"container-fluid\">\n");
        content.append("    <div class=\"card\">\n");
        content.append("        <div class=\"card-header\">\n");
        content.append("            <h5 class=\"card-title mb-0\">编辑").append(tableComment).append("</h5>\n");
        content.append("        </div>\n");
        content.append("        <div class=\"card-body\">\n");
        content.append("            <form id=\"").append(entityNameLower).append("Form\" method=\"post\">\n");

        // 生成表单字段
        content.append(generateFormFields(fields, "edit"));

        // 添加按钮
        content.append("                <div class=\"form-group row\">\n");
        content.append("                    <div class=\"col-sm-9 offset-sm-3\">\n");
        content.append("                        <button type=\"submit\" class=\"btn btn-primary\">\n");
        content.append("                            <i class=\"bi bi-floppy-fill\"></i> 保存\n");
        content.append("                        </button>\n");
        content.append("                        <a href=\"/").append(entityNameLower).append("List\" class=\"btn btn-secondary ml-2\">\n");
        content.append("                            <i class=\"bi bi-arrow-left\"></i> 返回\n");
        content.append("                        </a>\n");
        content.append("                    </div>\n");
        content.append("                </div>\n");
        content.append("            </form>\n");
        content.append("        </div>\n");
        content.append("    </div>\n");
        content.append("</div>\n");

        // 添加脚本（样式已在head.html中）
        content.append(generateSubmitScript(fields, tableName, "edit"));
        content.append(generateLoadScript(fields, tableName));

        return content.toString();
    }

    /**
     * 生成列表页面内容
     */
    private String generateListPageContent(List<Mores> fields, String tableName, String tableComment, Map<String, Boolean> tableFunctions) {
        StringBuilder content = new StringBuilder();
        String entityNameLower = toCamelCase(tableName);

        // 列表页面容器
        content.append("<div class=\"container-fluid\">\n");

        // 搜索表单（只有当有字段的mobt为1时才显示）
        if (hasSearchableFields(fields)) {
            content.append("    <div class=\"card mb-3\">\n");
            content.append("        <div class=\"card-body\">\n");
            content.append(generateSearchForm(fields));
            content.append("        </div>\n");
            content.append("    </div>\n");
        }

        // 去掉添加按钮区域

        // 数据表格
        content.append("    <div class=\"card\">\n");
        content.append("        <div class=\"card-header\">\n");
        content.append("            <h5 class=\"card-title mb-0\">").append(tableComment).append("列表</h5>\n");
        content.append("        </div>\n");
        content.append("        <div class=\"card-body\">\n");
        content.append("            <div class=\"table-responsive\">\n");
        content.append("                <table class=\"table table-striped table-hover\">\n");
        content.append("                    <thead class=\"thead-light\">\n");
        content.append("                        <tr>\n");
        content.append(generateTableHeaders(fields));

        // 只有当有操作功能时才显示操作列
        if (tableFunctions != null && (tableFunctions.getOrDefault("backendEdit", false) ||
            tableFunctions.getOrDefault("backendDetail", false) ||
            tableFunctions.getOrDefault("backendDelete", false))) {
            content.append("                            <th>操作</th>\n");
        }

        content.append("                        </tr>\n");
        content.append("                    </thead>\n");
        content.append("                    <tbody>\n");
        content.append("                        <tr th:each=\"item : ${list}\">\n");
        content.append(generateTableColumns(fields));

        // 根据表功能配置生成操作按钮
        if (tableFunctions != null) {
            content.append(generateActionButtons(tableName, tableFunctions, fields));
        }

        content.append("                        </tr>\n");
        content.append("                        <tr th:if=\"${#lists.isEmpty(list)}\">\n");
        content.append("                            <td colspan=\"100%\" class=\"text-center text-muted\">暂无数据</td>\n");
        content.append("                        </tr>\n");
        content.append("                    </tbody>\n");
        content.append("                </table>\n");
        content.append("            </div>\n");

        // 分页区域
        content.append("            <nav aria-label=\"分页导航\" th:if=\"${totalPages > 1}\">\n");
        content.append("                <ul class=\"pagination justify-content-center\">\n");
        content.append("                    <li class=\"page-item\" th:classappend=\"${currentPage == 1} ? 'disabled'\">\n");
        content.append("                        <a class=\"page-link\" th:href=\"@{''(page=${currentPage - 1})}\">上一页</a>\n");
        content.append("                    </li>\n");
        content.append("                    <li class=\"page-item\" th:each=\"i : ${#numbers.sequence(1, totalPages)}\" \n");
        content.append("                        th:classappend=\"${i == currentPage} ? 'active'\">\n");
        content.append("                        <a class=\"page-link\" th:href=\"@{''(page=${i})}\" th:text=\"${i}\"></a>\n");
        content.append("                    </li>\n");
        content.append("                    <li class=\"page-item\" th:classappend=\"${currentPage == totalPages} ? 'disabled'\">\n");
        content.append("                        <a class=\"page-link\" th:href=\"@{''(page=${currentPage + 1})}\">下一页</a>\n");
        content.append("                    </li>\n");
        content.append("                </ul>\n");
        content.append("            </nav>\n");
        content.append("        </div>\n");
        content.append("    </div>\n");
        content.append("</div>\n");

        // 添加脚本（样式已在head.html中）
        content.append(generatePaginationScript(tableName));

        return content.toString();
    }

    /**
     * 生成详情页面内容
     */
    private String generateDetailPageContent(List<Mores> fields, String tableName, String tableComment) {
        StringBuilder content = new StringBuilder();
        String entityNameLower = toCamelCase(tableName);

        // 详情页面容器
        content.append("<div class=\"container-fluid\">\n");
        content.append("    <div class=\"card\">\n");
        content.append("        <div class=\"card-header\">\n");
        content.append("            <h5 class=\"card-title mb-0\">").append(tableComment).append("详情</h5>\n");
        content.append("        </div>\n");
        content.append("        <div class=\"card-body\">\n");

        // 生成详情字段
        content.append(generateDetailFields(fields));

        // 添加按钮
        content.append("            <div class=\"form-group row\">\n");
        content.append("                <div class=\"col-sm-9 offset-sm-3\">\n");
        content.append("                    <a href=\"#\" onclick=\"history.back()\" class=\"btn btn-secondary ml-2\">\n");
        content.append("                        <i class=\"bi bi-arrow-left\"></i> 返回\n");
        content.append("                    </a>\n");
        content.append("                </div>\n");
        content.append("            </div>\n");
        content.append("        </div>\n");
        content.append("    </div>\n");
        content.append("</div>\n");

        // 添加脚本（样式已在head.html中）
        content.append(generateLoadScript(fields, tableName));

        return content.toString();
    }

    /**
     * 生成删除页面内容
     */
    private String generateDeletePageContent(List<Mores> fields, String tableName, String tableComment) {
        StringBuilder content = new StringBuilder();
        String entityNameLower = toCamelCase(tableName);

        // 删除页面容器
        content.append("<div class=\"container-fluid\">\n");
        content.append("    <div class=\"card\">\n");
        content.append("        <div class=\"card-header bg-danger text-white\">\n");
        content.append("            <h5 class=\"card-title mb-0\">删除").append(tableComment).append("</h5>\n");
        content.append("        </div>\n");
        content.append("        <div class=\"card-body\">\n");

        // 生成确认信息
        content.append(generateConfirmInfo(fields));

        // 删除表单
        content.append("            <form id=\"deleteForm\" method=\"post\">\n");
        content.append("                <input type=\"hidden\" name=\"id\" th:value=\"${item.id}\">\n");
        content.append("                <div class=\"form-group\">\n");
        content.append("                    <button type=\"button\" class=\"btn btn-danger\" onclick=\"confirmDelete()\">\n");
        content.append("                        <i class=\"bi bi-trash3\"></i> 确认删除\n");
        content.append("                    </button>\n");
        content.append("                    <a href=\"/").append(entityNameLower).append("Manage2\" class=\"btn btn-secondary ml-2\">\n");
        content.append("                        <i class=\"bi bi-arrow-left\"></i> 取消\n");
        content.append("                    </a>\n");
        content.append("                </div>\n");
        content.append("            </form>\n");
        content.append("        </div>\n");
        content.append("    </div>\n");
        content.append("</div>\n");

        // 添加脚本（样式已在head.html中）
        content.append(generateDeleteScript(tableName));

        return content.toString();
    }

    /**
     * 生成默认页面内容
     */
    private String generateDefaultPageContent(List<Mores> fields, String tableName, String tableComment) {
        StringBuilder content = new StringBuilder();

        content.append("<div class=\"container-fluid\">\n");
        content.append("    <div class=\"alert alert-info\">\n");
        content.append("        <h5>").append(tableComment).append("管理</h5>\n");
        content.append("        <p>该页面包含").append(tableComment).append("的基本信息和操作功能。</p>\n");
        content.append("    </div>\n");

        // 显示表格结构信息
        content.append("    <div class=\"card\">\n");
        content.append("        <div class=\"card-header\">\n");
        content.append("            <h5 class=\"card-title mb-0\">表结构信息</h5>\n");
        content.append("        </div>\n");
        content.append("        <div class=\"card-body\">\n");
        content.append("            <div class=\"table-responsive\">\n");
        content.append("                <table class=\"table table-bordered\">\n");
        content.append("                    <thead>\n");
        content.append("                        <tr>\n");
        content.append("                            <th>字段名</th>\n");
        content.append("                            <th>中文名</th>\n");
        content.append("                            <th>类型</th>\n");
        content.append("                        </tr>\n");
        content.append("                    </thead>\n");
        content.append("                    <tbody>\n");

        for (Mores field : fields) {
            content.append("                        <tr>\n");
            content.append("                            <td>").append(field.getMoname()).append("</td>\n");
            content.append("                            <td>").append(field.getMozname() != null ? field.getMozname() : "").append("</td>\n");
            content.append("                            <td>").append(field.getMotype()).append("</td>\n");
            content.append("                        </tr>\n");
        }

        content.append("                    </tbody>\n");
        content.append("                </table>\n");
        content.append("            </div>\n");
        content.append("        </div>\n");
        content.append("    </div>\n");
        content.append("</div>\n");

        // 样式已在head.html中

        return content.toString();
    }

    /**
     * 生成添加页面
     */
    private String generateAddPage(String content, List<Mores> fields, String tableName, String tableComment) {
        // 生成表单字段（排除主键）
        String formFields = generateFormFields(fields, "add");
        content = content.replace("$formFields$", formFields);
        
        // 生成表单提交脚本
        String submitScript = generateSubmitScript(fields, tableName, "add");
        content = content.replace("$submitScript$", submitScript);
        
        // 生成验证规则
        String validationRules = generateValidationRules(fields);
        content = content.replace("$validationRules$", validationRules);
        
        // 清空不需要的占位符
        content = content.replace("$tableHeaders$", "");
        content = content.replace("$tableColumns$", "");
        content = content.replace("$tableData$", "");
        
        return content;
    }

    /**
     * 生成编辑页面
     */
    private String generateEditPage(String content, List<Mores> fields, String tableName, String tableComment) {
        // 生成表单字段（包含主键但设为隐藏）
        String formFields = generateFormFields(fields, "edit");
        content = content.replace("$formFields$", formFields);
        
        // 生成数据加载脚本
        String loadScript = generateLoadScript(fields, tableName);
        content = content.replace("$loadScript$", loadScript);
        
        // 生成表单提交脚本
        String submitScript = generateSubmitScript(fields, tableName, "edit");
        content = content.replace("$submitScript$", submitScript);
        
        // 生成验证规则
        String validationRules = generateValidationRules(fields);
        content = content.replace("$validationRules$", validationRules);
        
        // 清空不需要的占位符
        content = content.replace("$tableHeaders$", "");
        content = content.replace("$tableColumns$", "");
        content = content.replace("$tableData$", "");
        
        return content;
    }

    /**
     * 生成列表页面
     */
    private String generateListPage(String content, List<Mores> fields, String tableName, String tableComment) {
        // 生成表格头部
        String tableHeaders = generateTableHeaders(fields);
        content = content.replace("$tableHeaders$", tableHeaders);
        
        // 生成表格列
        String tableColumns = generateTableColumns(fields);
        content = content.replace("$tableColumns$", tableColumns);
        
        // 生成搜索表单
        String searchForm = generateSearchForm(fields);
        content = content.replace("$searchForm$", searchForm);
        
        // 生成操作按钮
        String actionButtons = generateActionButtons(tableName);
        content = content.replace("$actionButtons$", actionButtons);
        
        // 生成分页脚本
        String paginationScript = generatePaginationScript(tableName);
        content = content.replace("$paginationScript$", paginationScript);
        
        // 清空不需要的占位符
        content = content.replace("$formFields$", "");
        content = content.replace("$submitScript$", "");
        content = content.replace("$validationRules$", "");
        
        return content;
    }

    /**
     * 生成详情页面
     */
    private String generateDetailPage(String content, List<Mores> fields, String tableName, String tableComment) {
        // 生成详情显示字段
        String detailFields = generateDetailFields(fields);
        content = content.replace("$detailFields$", detailFields);
        
        // 生成数据加载脚本
        String loadScript = generateLoadScript(fields, tableName);
        content = content.replace("$loadScript$", loadScript);
        
        // 清空不需要的占位符
        content = content.replace("$formFields$", "");
        content = content.replace("$tableHeaders$", "");
        content = content.replace("$tableColumns$", "");
        content = content.replace("$submitScript$", "");
        content = content.replace("$validationRules$", "");
        
        return content;
    }

    /**
     * 生成删除页面
     */
    private String generateDeletePage(String content, List<Mores> fields, String tableName, String tableComment) {
        // 生成确认信息显示
        String confirmInfo = generateConfirmInfo(fields);
        content = content.replace("$confirmInfo$", confirmInfo);
        
        // 生成删除脚本
        String deleteScript = generateDeleteScript(tableName);
        content = content.replace("$deleteScript$", deleteScript);
        
        // 清空不需要的占位符
        content = content.replace("$formFields$", "");
        content = content.replace("$tableHeaders$", "");
        content = content.replace("$tableColumns$", "");
        content = content.replace("$submitScript$", "");
        content = content.replace("$validationRules$", "");
        
        return content;
    }

    /**
     * 生成默认页面
     */
    private String generateDefaultPage(String content, List<Mores> fields, String tableName, String tableComment) {
        // 生成基础内容
        String formFields = generateFormFields(fields, "default");
        content = content.replace("$formFields$", formFields);
        
        String tableHeaders = generateTableHeaders(fields);
        content = content.replace("$tableHeaders$", tableHeaders);
        
        String tableColumns = generateTableColumns(fields);
        content = content.replace("$tableColumns$", tableColumns);
        
        return content;
    }

    /**
     * 清空占位符
     */
    private String clearPlaceholders(String content) {
        content = content.replace("$formFields$", "");
        content = content.replace("$tableHeaders$", "");
        content = content.replace("$tableColumns$", "");
        content = content.replace("$tableData$", "");
        content = content.replace("$submitScript$", "");
        content = content.replace("$validationRules$", "");
        content = content.replace("$loadScript$", "");
        content = content.replace("$searchForm$", "");
        content = content.replace("$actionButtons$", "");
        content = content.replace("$paginationScript$", "");
        content = content.replace("$detailFields$", "");
        content = content.replace("$confirmInfo$", "");
        content = content.replace("$deleteScript$", "");
        return content;
    }

    /**
     * 转换为PascalCase（首字母大写的驼峰命名）
     */
    private String toPascalCase(String str) {
        if (str == null || str.isEmpty()) return str;
        return str.substring(0, 1).toUpperCase() + toCamelCase(str.substring(1));
    }

    /**
     * 转换为camelCase（首字母小写的驼峰命名）
     */
    private String toCamelCase(String str) {
        if (str == null || str.isEmpty()) return str;
        
        StringBuilder result = new StringBuilder();
        boolean capitalizeNext = false;
        
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c == '_' || c == '-') {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                result.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                result.append(Character.toLowerCase(c));
            }
        }
        
        return result.toString();
    }

    /**
     * 生成表单字段HTML
     */
    private String generateFormFields(List<Mores> fields, String pageType) {
        StringBuilder formFields = new StringBuilder();

        for (int i = 0; i < fields.size(); i++) {
            Mores field = fields.get(i);
            String fieldName = field.getMoname();
            String fieldComment = field.getMozname() != null ? field.getMozname() : fieldName;
            String fieldType = field.getMotype();
            String fieldCamelName = toCamelCase(fieldName);
            String controlType = field.getMoflag(); // 控件类型

            // 如果是自动当前时间字段，在添加和编辑页面都忽略
            if ("自动当前时间".equals(controlType)) {
                continue;
            }

            // 处理第一个字段的特殊逻辑
            if (i == 0) {
                if ("edit".equals(pageType)) {
                    // 编辑页面：如果第一个字段是int类型，忽略；如果是varchar类型，设为readonly
                    if (fieldType.toLowerCase().contains("int")) {
                        // int类型字段忽略，但添加隐藏字段用于提交
                        formFields.append("                        <input type=\"hidden\" id=\"").append(fieldName)
                                  .append("\" name=\"").append(fieldName).append("\" th:value=\"${item.").append(fieldCamelName).append("}\">\n");
                        continue;
                    } else if (fieldType.toLowerCase().contains("varchar")) {
                        // varchar类型字段设为readonly
                        formFields.append("                <div class=\"form-group row\">\n");
                        formFields.append("                    <label for=\"").append(fieldName).append("\" class=\"col-sm-3 col-form-label\">").append(fieldComment).append("</label>\n");
                        formFields.append("                    <div class=\"col-sm-9\">\n");
                        formFields.append("                        <input type=\"text\" class=\"form-control\" id=\"").append(fieldName)
                                  .append("\" name=\"").append(fieldName).append("\" readonly th:value=\"${item.").append(fieldCamelName).append("}\">\n");
                        formFields.append("                    </div>\n");
                        formFields.append("                </div>\n");
                        continue;
                    }
                } else if ("add".equals(pageType) && fieldType.toLowerCase().contains("int")) {
                    // 添加页面跳过int类型的第一个字段（通常是自增主键）
                    continue;
                }
            }

            // 在添加页面跳过主键字段（保持原有逻辑）
            if ("add".equals(pageType) && isPrimaryKey(field)) {
                continue;
            }

            formFields.append("                <div class=\"form-group row mb-3\">\n");
            formFields.append("                    <label for=\"").append(fieldName).append("\" class=\"col-sm-3 col-form-label\">").append(fieldComment);

            // 检查是否为必填字段
            if ("1".equals(field.getMoyz())) {
                formFields.append(" <span class=\"required\">*</span>");
            }

            formFields.append("</label>\n");
            formFields.append("                    <div class=\"col-sm-9\">\n");

            // 在编辑页面，主键字段设为隐藏（保持原有逻辑）
            if ("edit".equals(pageType) && isPrimaryKey(field)) {
                formFields.append("                        <input type=\"hidden\" id=\"").append(fieldName)
                          .append("\" name=\"").append(fieldName).append("\" th:value=\"${item.").append(fieldCamelName).append("}\">\n");
                formFields.append("                        <input type=\"text\" class=\"form-control\" readonly th:value=\"${item.").append(fieldCamelName).append("}\">\n");
            } else {
                // 根据字段类型生成不同的输入控件
                String inputControl = generateInputControl(field, pageType);
                formFields.append(inputControl);
            }

            formFields.append("                    </div>\n");
            formFields.append("                </div>\n");

            // 在注册模式下，如果当前字段是密码字段，则在其后面添加确认密码字段
            if ("register".equals(pageType) && fieldComment.contains("密码")) {
                formFields.append("                <div class=\"form-group row mb-3\">\n");
                formFields.append("                    <label for=\"pwd2\" class=\"col-sm-3 col-form-label\">确认密码</label>\n");
                formFields.append("                    <div class=\"col-sm-9\">\n");
                formFields.append("                        <input type=\"password\" class=\"form-control\" id=\"pwd2\" name=\"pwd2\">\n");
                formFields.append("                    </div>\n");
                formFields.append("                </div>\n");
            }
        }

        return formFields.toString();
    }

    /**
     * 生成输入控件
     */
    private String generateInputControl(Mores field, String pageType) {
        String fieldName = field.getMoname();
        String fieldType = field.getMotype().toLowerCase();
        String fieldCamelName = toCamelCase(fieldName);
        String fieldComment = field.getMozname() != null ? field.getMozname() : fieldName;
        String controlType = field.getMoflag(); // 控件类型

        StringBuilder control = new StringBuilder();

        // 根据控件类型(moflag)生成不同的控件
        if ("多行文本".equals(controlType)) {
            // 多行文本框
            control.append("                        <textarea class=\"form-control\" id=\"").append(fieldName)
                   .append("\" name=\"").append(fieldName).append("\" rows=\"4\"");
            if ("edit".equals(pageType)) {
                control.append(" th:text=\"${item.").append(fieldCamelName).append("}\"");
            }
            control.append("></textarea>\n");
        } else if ("下拉框".equals(controlType)) {
            // 下拉选择框
            control.append("                        <select class=\"form-control\" id=\"").append(fieldName)
                   .append("\" name=\"").append(fieldName).append("\"");
            if ("edit".equals(pageType)) {
                control.append(" th:value=\"${item.").append(fieldCamelName).append("}\"");
            }
            control.append(">\n");
            control.append("                            <option value=\"\">请选择</option>\n");
            control.append("                            <!-- 这里需要根据实际需求添加选项 -->\n");
            control.append("                        </select>\n");
        } else if ("单选按钮".equals(controlType)) {
            // 单选按钮组
            control.append("                        <div class=\"form-check-inline\">\n");
            control.append("                            <input class=\"form-check-input\" type=\"radio\" name=\"").append(fieldName).append("\" id=\"").append(fieldName).append("1\" value=\"1\">\n");
            control.append("                            <label class=\"form-check-label\" for=\"").append(fieldName).append("1\">选项1</label>\n");
            control.append("                        </div>\n");
            control.append("                        <div class=\"form-check-inline\">\n");
            control.append("                            <input class=\"form-check-input\" type=\"radio\" name=\"").append(fieldName).append("\" id=\"").append(fieldName).append("2\" value=\"2\">\n");
            control.append("                            <label class=\"form-check-label\" for=\"").append(fieldName).append("2\">选项2</label>\n");
            control.append("                        </div>\n");
        } else if ("复选框".equals(controlType)) {
            // 复选框
            control.append("                        <div class=\"form-check\">\n");
            control.append("                            <input class=\"form-check-input\" type=\"checkbox\" id=\"").append(fieldName).append("\" name=\"").append(fieldName).append("\" value=\"1\"");
            if ("edit".equals(pageType)) {
                control.append(" th:checked=\"${item.").append(fieldCamelName).append(" == '1'}\"");
            }
            control.append(">\n");
            control.append("                            <label class=\"form-check-label\" for=\"").append(fieldName).append("\">").append(fieldComment).append("</label>\n");
            control.append("                        </div>\n");
        } else if ("日期选择".equals(controlType)) {
            // 日期选择器
            control.append("                        <input type=\"date\" class=\"form-control\" id=\"").append(fieldName)
                   .append("\" name=\"").append(fieldName).append("\"");
            if ("edit".equals(pageType)) {
                control.append(" th:value=\"${item.").append(fieldCamelName).append("}\"");
            }
            control.append(">\n");
        } else if ("时间选择".equals(controlType)) {
            // 时间选择器
            control.append("                        <input type=\"datetime-local\" class=\"form-control\" id=\"").append(fieldName)
                   .append("\" name=\"").append(fieldName).append("\"");
            if ("edit".equals(pageType)) {
                control.append(" th:value=\"${item.").append(fieldCamelName).append("}\"");
            }
            control.append(">\n");
        } else if ("文件上传".equals(controlType)) {
            // 文件上传
            control.append("                        <div class=\"file-upload-container\">\n");
            control.append("                            <input type=\"file\" class=\"form-control\" id=\"").append(fieldName)
                   .append("\" name=\"").append(fieldName).append("\" onchange=\"uploadFile(this, '").append(fieldName).append("')\">\n");
            control.append("                            <input type=\"hidden\" id=\"").append(fieldName).append("_hidden\" name=\"").append(fieldName).append("_path\"");
            if ("edit".equals(pageType)) {
                control.append(" th:value=\"${item.").append(fieldCamelName).append("}\"");
            }
            control.append(">\n");
            if ("edit".equals(pageType)) {
                control.append("                            <div class=\"current-file mt-2\" th:if=\"${item.").append(fieldCamelName).append("}\">\n");
                control.append("                                <small class=\"text-muted\">当前文件: </small>\n");
                control.append("                                <a th:href=\"@{/downFile(filePath=${item.").append(fieldCamelName).append("})}\" th:text=\"${item.").append(fieldCamelName).append("}\" class=\"btn btn-sm btn-outline-primary\">下载文件</a>\n");
                control.append("                            </div>\n");
            }
            control.append("                        </div>\n");
        } else if ("图片上传".equals(controlType)) {
            // 图片上传
            control.append("                        <div class=\"image-upload-container\">\n");
            control.append("                            <input type=\"file\" class=\"form-control\" id=\"").append(fieldName)
                   .append("\" name=\"").append(fieldName).append("\" accept=\"image/*\" onchange=\"uploadImage(this, '").append(fieldName).append("')\">\n");
            control.append("                            <input type=\"hidden\" id=\"").append(fieldName).append("_hidden\" name=\"").append(fieldName).append("_path\"");
            if ("edit".equals(pageType)) {
                control.append(" th:value=\"${item.").append(fieldCamelName).append("}\"");
            }
            control.append(">\n");
            if ("edit".equals(pageType)) {
                control.append("                            <div class=\"current-image mt-2\" th:if=\"${item.").append(fieldCamelName).append("}\">\n");
                control.append("                                <small class=\"text-muted\">当前图片: </small><br>\n");
                control.append("                                <img th:src=\"@{'/upload/' + ${item.").append(fieldCamelName).append("}}\" alt=\"当前图片\" style=\"max-width: 150px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 5px;\">\n");
                control.append("                            </div>\n");
            }
            control.append("                        </div>\n");
        } else if ("自动当前时间".equals(controlType)) {
            // 自动当前时间（只读显示）
            control.append("                        <input type=\"text\" class=\"form-control\" id=\"").append(fieldName)
                   .append("\" name=\"").append(fieldName).append("\" readonly");
            if ("edit".equals(pageType)) {
                control.append(" th:value=\"${item.").append(fieldCamelName).append("}\"");
            } else {
                control.append(" placeholder=\"系统自动生成\"");
            }
            control.append(">\n");
        } else {
            // 根据字段类型和字段名称生成控件（保持原有逻辑）
            if (fieldType.contains("text") || fieldType.contains("longtext")) {
                // 文本域
                control.append("                        <textarea class=\"form-control\" id=\"").append(fieldName)
                       .append("\" name=\"").append(fieldName).append("\" rows=\"3\"");
                if ("edit".equals(pageType)) {
                    control.append(" th:text=\"${item.").append(fieldCamelName).append("}\"");
                }
                control.append("></textarea>\n");
            } else if (fieldType.contains("date")) {
                // 日期控件
                control.append("                        <input type=\"date\" class=\"form-control\" id=\"").append(fieldName)
                       .append("\" name=\"").append(fieldName).append("\"");
                if ("edit".equals(pageType)) {
                    control.append(" th:value=\"${item.").append(fieldCamelName).append("}\"");
                }
                control.append(">\n");
            } else if (fieldType.contains("time") || fieldType.contains("datetime")) {
                // 时间控件
                control.append("                        <input type=\"datetime-local\" class=\"form-control\" id=\"").append(fieldName)
                       .append("\" name=\"").append(fieldName).append("\"");
                if ("edit".equals(pageType)) {
                    control.append(" th:value=\"${item.").append(fieldCamelName).append("}\"");
                }
                control.append(">\n");
            } else if (fieldType.contains("int") || fieldType.contains("decimal") || fieldType.contains("float") || fieldType.contains("double")) {
                // 数字控件
                control.append("                        <input type=\"number\" class=\"form-control\" id=\"").append(fieldName)
                       .append("\" name=\"").append(fieldName).append("\"");
                if ("edit".equals(pageType)) {
                    control.append(" th:value=\"${item.").append(fieldCamelName).append("}\"");
                }
                control.append(">\n");
            } else if (fieldComment.contains("密码")) {
                // 密码控件
                control.append("                        <input type=\"password\" class=\"form-control\" id=\"").append(fieldName)
                       .append("\" name=\"").append(fieldName).append("\"");
                if ("edit".equals(pageType)) {
                    control.append(" th:value=\"${item.").append(fieldCamelName).append("}\"");
                }
                control.append(">\n");
            } else if (fieldComment.contains("邮箱") || fieldComment.contains("邮件")) {
                // 邮箱控件
                control.append("                        <input type=\"email\" class=\"form-control\" id=\"").append(fieldName)
                       .append("\" name=\"").append(fieldName).append("\"");
                if ("edit".equals(pageType)) {
                    control.append(" th:value=\"${item.").append(fieldCamelName).append("}\"");
                }
                control.append(">\n");
            } else if (fieldComment.contains("手机") || fieldComment.contains("电话") || fieldComment.contains("联系方式")) {
                // 手机号控件
                control.append("                        <input type=\"tel\" class=\"form-control\" id=\"").append(fieldName)
                       .append("\" name=\"").append(fieldName).append("\" pattern=\"[0-9]{11}\" placeholder=\"请输入11位手机号\"");
                if ("edit".equals(pageType)) {
                    control.append(" th:value=\"${item.").append(fieldCamelName).append("}\"");
                }
                control.append(">\n");
            } else {
                // 默认文本控件
                control.append("                        <input type=\"text\" class=\"form-control\" id=\"").append(fieldName)
                       .append("\" name=\"").append(fieldName).append("\"");
                if ("edit".equals(pageType)) {
                    control.append(" th:value=\"${item.").append(fieldCamelName).append("}\"");
                }
                control.append(">\n");
            }
        }

        // 不再添加required属性，改为使用JavaScript验证

        return control.toString();
    }

    /**
     * 生成表格头部HTML
     */
    private String generateTableHeaders(List<Mores> fields) {
        StringBuilder headers = new StringBuilder();

        for (int i = 0; i < fields.size(); i++) {
            Mores field = fields.get(i);
            String fieldType = field.getMotype().toLowerCase();

            // 如果第1个字段是int类型，就不显示
            if (i == 0 && fieldType.contains("int")) {
                continue;
            }

            String fieldComment = field.getMozname() != null ? field.getMozname() : field.getMoname();
            headers.append("                    <th>").append(fieldComment).append("</th>\n");
        }

        // 不在这里添加操作列，由调用方决定是否需要

        return headers.toString();
    }

    /**
     * 生成表格列HTML
     */
    private String generateTableColumns(List<Mores> fields) {
        StringBuilder columns = new StringBuilder();

        for (int i = 0; i < fields.size(); i++) {
            Mores field = fields.get(i);
            String fieldName = field.getMoname();
            String fieldCamelName = toCamelCase(fieldName);
            String fieldType = field.getMotype().toLowerCase();
            String controlType = field.getMoflag(); // 控件类型

            // 如果第1个字段是int类型，就不显示
            if (i == 0 && fieldType.contains("int")) {
                continue;
            }

            columns.append("                    <td");

            // 根据控件类型和字段类型添加特殊处理
            if ("图片上传".equals(controlType)) {
                // 图片字段显示60x60缩略图
                columns.append(">\n");
                columns.append("                        <div th:if=\"${item.").append(fieldCamelName).append("}\">\n");
                columns.append("                            <img th:src=\"@{'/upload/' + ${item.").append(fieldCamelName).append("}}\" alt=\"图片\" style=\"width: 60px; height: 60px; object-fit: cover; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;\" onclick=\"previewImage(this.src)\">\n");
                columns.append("                        </div>\n");
                columns.append("                        <span th:unless=\"${item.").append(fieldCamelName).append("}\">无图片</span>\n");
                columns.append("                    ");
            } else if ("文件上传".equals(controlType)) {
                // 文件字段显示下载链接
                columns.append(">\n");
                columns.append("                        <div th:if=\"${item.").append(fieldCamelName).append("}\">\n");
                columns.append("                            <a th:href=\"@{/downFile(filePath=${item.").append(fieldCamelName).append("})}\" th:text=\"${item.").append(fieldCamelName).append("}\" class=\"btn btn-sm btn-outline-primary\">下载</a>\n");
                columns.append("                        </div>\n");
                columns.append("                        <span th:unless=\"${item.").append(fieldCamelName).append("}\">无文件</span>\n");
                columns.append("                    ");
            } else if (fieldType.contains("date") || fieldType.contains("time")) {
                columns.append(" th:text=\"${#dates.format(item.").append(fieldCamelName).append(", 'yyyy-MM-dd HH:mm:ss')}\"");
            } else if (field.getMozname() != null && field.getMozname().contains("密码")) {
                columns.append(">******");
            } else {
                columns.append(" th:text=\"${item.").append(fieldCamelName).append("}\"");
            }

            columns.append("></td>\n");
        }

        return columns.toString();
    }

    /**
     * 判断是否为主键字段
     */
    private boolean isPrimaryKey(Mores field) {
        String fieldName = field.getMoname().toLowerCase();
        return fieldName.equals("id") || fieldName.endsWith("id") ||
               (field.getMozname() != null && (field.getMozname().contains("ID") || field.getMozname().contains("编号")));
    }

    /**
     * 生成详情显示字段
     */
    private String generateDetailFields(List<Mores> fields) {
        StringBuilder detailFields = new StringBuilder();

        for (Mores field : fields) {
            String fieldName = field.getMoname();
            String fieldComment = field.getMozname() != null ? field.getMozname() : fieldName;
            String fieldCamelName = toCamelCase(fieldName);
            String fieldType = field.getMotype().toLowerCase();
            String controlType = field.getMoflag(); // 控件类型

            detailFields.append("                <div class=\"form-group row mb-3\">\n");
            detailFields.append("                    <label class=\"col-sm-3 col-form-label fw-bold\">").append(fieldComment).append(":</label>\n");
            detailFields.append("                    <div class=\"col-sm-9\">\n");

            // 根据控件类型和字段类型添加特殊处理
            if ("图片上传".equals(controlType)) {
                // 图片字段显示180x180图片
                detailFields.append("                        <div th:if=\"${item.").append(fieldCamelName).append("}\">\n");
                detailFields.append("                            <img th:src=\"@{'/upload/' + ${item.").append(fieldCamelName).append("}}\" alt=\"").append(fieldComment).append("\" style=\"width: 180px; height: 180px; object-fit: cover; border: 1px solid #ddd; border-radius: 4px; cursor: pointer;\" onclick=\"previewImage(this.src)\">\n");
                detailFields.append("                        </div>\n");
                detailFields.append("                        <span th:unless=\"${item.").append(fieldCamelName).append("}\">无图片</span>\n");
            } else if ("文件上传".equals(controlType)) {
                // 文件字段显示下载链接
                detailFields.append("                        <div th:if=\"${item.").append(fieldCamelName).append("}\">\n");
                detailFields.append("                            <a th:href=\"@{/downFile(filePath=${item.").append(fieldCamelName).append("})}\" th:text=\"${item.").append(fieldCamelName).append("}\" class=\"btn btn-outline-primary\">下载文件</a>\n");
                detailFields.append("                        </div>\n");
                detailFields.append("                        <span th:unless=\"${item.").append(fieldCamelName).append("}\">无文件</span>\n");
            } else {
                detailFields.append("                        <p class=\"form-control-plaintext border-bottom pb-2\"");

                // 根据字段类型添加特殊处理
                if (fieldType.contains("date") || fieldType.contains("time")) {
                    detailFields.append(" th:text=\"${#dates.format(item.").append(fieldCamelName).append(", 'yyyy-MM-dd HH:mm:ss')}\"");
                } else if (field.getMozname() != null && field.getMozname().contains("密码")) {
                    detailFields.append(">******");
                } else {
                    detailFields.append(" th:text=\"${item.").append(fieldCamelName).append("}\"");
                }

                detailFields.append("></p>\n");
            }

            detailFields.append("                    </div>\n");
            detailFields.append("                </div>\n");
        }

        return detailFields.toString();
    }

    /**
     * 检查是否有可搜索的字段（mobt为1）
     */
    private boolean hasSearchableFields(List<Mores> fields) {
        return fields.stream().anyMatch(field -> "1".equals(field.getMobt()));
    }

    /**
     * 生成搜索表单
     */
    private String generateSearchForm(List<Mores> fields) {
        StringBuilder searchForm = new StringBuilder();

        searchForm.append("            <form class=\"search-form\" method=\"get\">\n");
        searchForm.append("                <div class=\"row g-3 align-items-end\">\n");

        // 只为mobt为1的字段生成搜索框
        List<Mores> searchableFields = fields.stream()
            .filter(field -> "1".equals(field.getMobt()))
            .collect(Collectors.toList());

        for (Mores field : searchableFields) {
            String fieldName = field.getMoname();
            String fieldComment = field.getMozname() != null ? field.getMozname() : fieldName;

            searchForm.append("                    <div class=\"col-md-3\">\n");
            searchForm.append("                        <label for=\"search_").append(fieldName).append("\" class=\"form-label\">").append(fieldComment).append("</label>\n");
            searchForm.append("                        <input type=\"text\" class=\"form-control\" id=\"search_").append(fieldName)
                      .append("\" name=\"").append(fieldName).append("\" placeholder=\"请输入").append(fieldComment).append("\" th:value=\"${param.").append(fieldName).append("}\">\n");
            searchForm.append("                    </div>\n");
        }

        searchForm.append("                    <div class=\"col-md-3\">\n");
        searchForm.append("                        <button type=\"submit\" class=\"btn btn-primary me-2\">\n");
        searchForm.append("                            <i class=\"bi bi-search\"></i> 搜索\n");
        searchForm.append("                        </button>\n");
        searchForm.append("                        <a href=\"?\" class=\"btn btn-secondary\">\n");
        searchForm.append("                            <i class=\"bi bi-arrow-clockwise\"></i> 重置\n");
        searchForm.append("                        </a>\n");
        searchForm.append("                    </div>\n");
        searchForm.append("                </div>\n");
        searchForm.append("            </form>\n");

        return searchForm.toString();
    }

    /**
     * 生成操作按钮
     */
    private String generateActionButtons(String tableName) {
        StringBuilder buttons = new StringBuilder();
        String entityNameLower = toCamelCase(tableName);

        buttons.append("                    <td>\n");
        buttons.append("                        <a th:href=\"@{/").append(entityNameLower).append("ToEdit(id=${item.").append(getPrimaryKeyField(tableName)).append("})}\" class=\"btn btn-sm btn-primary\">编辑</a>\n");
        buttons.append("                        <a th:href=\"@{/").append(entityNameLower).append("ToDetail(id=${item.").append(getPrimaryKeyField(tableName)).append("})}\" class=\"btn btn-sm btn-info\">详情</a>\n");
        buttons.append("                        <a th:href=\"@{/").append(entityNameLower).append("ToDel(id=${item.").append(getPrimaryKeyField(tableName)).append("})}\" class=\"btn btn-sm btn-danger\" onclick=\"return confirm('确定要删除吗？')\">删除</a>\n");
        buttons.append("                    </td>\n");

        return buttons.toString();
    }

    /**
     * 根据表功能配置生成操作按钮
     */
    private String generateActionButtons(String tableName, Map<String, Boolean> tableFunctions, List<Mores> fields) {
        StringBuilder buttons = new StringBuilder();
        String entityNameLower = toCamelCase(tableName);
        String firstFieldName = getFirstFieldName(fields);

        buttons.append("                    <td>\n");

        // 详情按钮放在编辑按钮前面
        if (tableFunctions.getOrDefault("backendDetail", false)) {
            buttons.append("                        <a th:href=\"@{/").append(entityNameLower).append("ToDetail(id=${item.").append(firstFieldName).append("})}\" class=\"btn btn-sm btn-info me-1\">\n");
            buttons.append("                            <i class=\"bi bi-eye-fill\"></i> 详情\n");
            buttons.append("                        </a>\n");
        }

        // 只有选中后台编辑才显示编辑按钮
        if (tableFunctions.getOrDefault("backendEdit", false)) {
            buttons.append("                        <a th:href=\"@{/").append(entityNameLower).append("ToEdit(id=${item.").append(firstFieldName).append("})}\" class=\"btn btn-sm btn-primary me-1\">\n");
            buttons.append("                            <i class=\"bi bi-pencil-square\"></i> 编辑\n");
            buttons.append("                        </a>\n");
        }

        // 只有选中后台删除才显示删除按钮
        if (tableFunctions.getOrDefault("backendDelete", false)) {
            // 获取第一个字段名作为主键
            String primaryKeyField = fields.get(0).getMoname();
            String primaryKeyCamelCase = toCamelCase(primaryKeyField);

            buttons.append("                        <a href=\"javascript:void(0)\" th:data-id=\"${item.").append(primaryKeyCamelCase).append("}\" onclick=\"deleteData('").append(entityNameLower).append("Del',this)\" class=\"btn btn-sm btn-danger\">\n");
            buttons.append("                            <i class=\"bi bi-trash3\"></i> 删除\n");
            buttons.append("                        </a>\n");
        }

        // 如果没有任何操作按钮，显示一个占位符
        if (!tableFunctions.getOrDefault("backendEdit", false) &&
            !tableFunctions.getOrDefault("backendDetail", false) &&
            !tableFunctions.getOrDefault("backendDelete", false)) {
            buttons.append("                        <span class=\"text-muted\">无操作</span>\n");
        }

        buttons.append("                    </td>\n");

        return buttons.toString();
    }

    /**
     * 获取表的第一个字段名（驼峰命名）
     */
    private String getFirstFieldName(List<Mores> fields) {
        if (fields != null && !fields.isEmpty()) {
            return toCamelCase(fields.get(0).getMoname());
        }
        return "id"; // 默认返回id
    }

    /**
     * 生成确认信息显示
     */
    private String generateConfirmInfo(List<Mores> fields) {
        StringBuilder confirmInfo = new StringBuilder();

        confirmInfo.append("            <div class=\"alert alert-warning\">\n");
        confirmInfo.append("                <h5>确认删除以下记录？</h5>\n");
        confirmInfo.append("            </div>\n");

        // 显示关键字段信息
        List<Mores> keyFields = fields.stream()
            .filter(field -> isPrimaryKey(field) ||
                           (field.getMozname() != null &&
                            (field.getMozname().contains("名称") || field.getMozname().contains("标题") || field.getMozname().contains("用户名"))))
            .limit(3)
            .collect(Collectors.toList());

        for (Mores field : keyFields) {
            String fieldComment = field.getMozname() != null ? field.getMozname() : field.getMoname();
            String fieldCamelName = toCamelCase(field.getMoname());

            confirmInfo.append("            <div class=\"form-group row\">\n");
            confirmInfo.append("                <label class=\"col-sm-2 col-form-label\">").append(fieldComment).append(":</label>\n");
            confirmInfo.append("                <div class=\"col-sm-10\">\n");
            confirmInfo.append("                    <p class=\"form-control-plaintext\" th:text=\"${item.").append(fieldCamelName).append("}\"></p>\n");
            confirmInfo.append("                </div>\n");
            confirmInfo.append("            </div>\n");
        }

        return confirmInfo.toString();
    }

    /**
     * 生成表单提交脚本
     */
    private String generateSubmitScript(List<Mores> fields, String tableName, String action) {
        StringBuilder script = new StringBuilder();
        String entityNameLower = toCamelCase(tableName);

        script.append("        <script>\n");

        // 添加文件上传和图片预览函数
        script.append("        // 文件上传函数\n");
        script.append("        function uploadFile(input, fieldName) {\n");
        script.append("            if (input.files && input.files[0]) {\n");
        script.append("                var formData = new FormData();\n");
        script.append("                formData.append('file', input.files[0]);\n");
        script.append("                formData.append('c', fieldName);\n");
        script.append("                \n");
        script.append("                $.ajax({\n");
        script.append("                    url: '/upload_re',\n");
        script.append("                    type: 'POST',\n");
        script.append("                    data: formData,\n");
        script.append("                    processData: false,\n");
        script.append("                    contentType: false,\n");
        script.append("                    success: function(response) {\n");
        script.append("                        // 假设返回的是文件名\n");
        script.append("                        $('#' + fieldName + '_hidden').val(response.fileName || response);\n");
        script.append("                        CommonUtils.showToast('文件上传成功', 'success');\n");
        script.append("                    },\n");
        script.append("                    error: function() {\n");
        script.append("                        CommonUtils.showToast('文件上传失败', 'error');\n");
        script.append("                    }\n");
        script.append("                });\n");
        script.append("            }\n");
        script.append("        }\n");
        script.append("        \n");
        script.append("        // 图片上传函数\n");
        script.append("        function uploadImage(input, fieldName) {\n");
        script.append("            if (input.files && input.files[0]) {\n");
        script.append("                var formData = new FormData();\n");
        script.append("                formData.append('file', input.files[0]);\n");
        script.append("                formData.append('c', fieldName);\n");
        script.append("                \n");
        script.append("                $.ajax({\n");
        script.append("                    url: '/upload_re',\n");
        script.append("                    type: 'POST',\n");
        script.append("                    data: formData,\n");
        script.append("                    processData: false,\n");
        script.append("                    contentType: false,\n");
        script.append("                    success: function(response) {\n");
        script.append("                        // 假设返回的是文件名\n");
        script.append("                        $('#' + fieldName + '_hidden').val(response.fileName || response);\n");
        script.append("                        CommonUtils.showToast('图片上传成功', 'success');\n");
        script.append("                        \n");
        script.append("                        // 显示预览图片\n");
        script.append("                        var previewContainer = $('#' + fieldName).closest('.image-upload-container').find('.current-image');\n");
        script.append("                        if (previewContainer.length === 0) {\n");
        script.append("                            $('#' + fieldName).closest('.image-upload-container').append('<div class=\"current-image mt-2\"><small class=\"text-muted\">当前图片: </small><br><img src=\"/upload/' + (response.fileName || response) + '\" alt=\"预览图片\" style=\"max-width: 150px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px; padding: 5px;\"></div>');\n");
        script.append("                        } else {\n");
        script.append("                            previewContainer.find('img').attr('src', '/upload/' + (response.fileName || response));\n");
        script.append("                        }\n");
        script.append("                    },\n");
        script.append("                    error: function() {\n");
        script.append("                        CommonUtils.showToast('图片上传失败', 'error');\n");
        script.append("                    }\n");
        script.append("                });\n");
        script.append("            }\n");
        script.append("        }\n");
        script.append("        \n");
        script.append("        // 图片预览函数\n");
        script.append("        function previewImage(src) {\n");
        script.append("            var modal = '<div class=\"modal fade\" id=\"imagePreviewModal\" tabindex=\"-1\">' +\n");
        script.append("                       '<div class=\"modal-dialog modal-lg modal-dialog-centered\">' +\n");
        script.append("                       '<div class=\"modal-content\">' +\n");
        script.append("                       '<div class=\"modal-header\">' +\n");
        script.append("                       '<h5 class=\"modal-title\">图片预览</h5>' +\n");
        script.append("                       '<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\"></button>' +\n");
        script.append("                       '</div>' +\n");
        script.append("                       '<div class=\"modal-body text-center\">' +\n");
        script.append("                       '<img src=\"' + src + '\" class=\"img-fluid\" style=\"max-height: 70vh;\">' +\n");
        script.append("                       '</div>' +\n");
        script.append("                       '</div></div></div>';\n");
        script.append("            \n");
        script.append("            $('body').append(modal);\n");
        script.append("            var modalInstance = new bootstrap.Modal(document.getElementById('imagePreviewModal'));\n");
        script.append("            modalInstance.show();\n");
        script.append("            \n");
        script.append("            $('#imagePreviewModal').on('hidden.bs.modal', function() {\n");
        script.append("                $(this).remove();\n");
        script.append("            });\n");
        script.append("        }\n");
        script.append("        \n");
        script.append("        $(document).ready(function() {\n");
        script.append("            $('#").append(entityNameLower).append("Form').on('submit', function(e) {\n");
        script.append("                e.preventDefault();\n");
        script.append("                \n");

        // 生成必填字段验证
        script.append(generateFieldValidation(fields, action));

        script.append("                \n");
        script.append("                // 使用通用表单提交方法\n");
        script.append("                CommonUtils.submitForm('#").append(entityNameLower).append("Form', \n");
        script.append("                    '/").append(entityNameLower).append(action.equals("add") ? "Add" : "Edit").append("', \n");
        script.append("                    function(response) {\n");
        script.append("                        // 成功回调\n");
        script.append("                        CommonUtils.showToast('操作成功！', 'success');\n");
        script.append("                        setTimeout(function() {\n");
        script.append("                            window.location.href = '/").append(entityNameLower).append("List';\n");
        script.append("                        }, 1500);\n");
        script.append("                    },\n");
        script.append("                    function(error) {\n");
        script.append("                        // 错误回调\n");
        script.append("                        console.error('提交失败:', error);\n");
        script.append("                    }\n");
        script.append("                );\n");
        script.append("            });\n");
        script.append("        });\n");
        script.append("        </script>\n");

        return script.toString();
    }

    /**
     * 生成字段验证JavaScript代码
     */
    private String generateFieldValidation(List<Mores> fields, String action) {
        StringBuilder validation = new StringBuilder();

        // 收集必填字段和需要特殊验证的字段
        List<Mores> requiredFields = new ArrayList<>();
        List<Mores> emailFields = new ArrayList<>();
        List<Mores> phoneFields = new ArrayList<>();

        for (int i = 0; i < fields.size(); i++) {
            Mores field = fields.get(i);
            String controlType = field.getMoflag();
            String fieldComment = field.getMozname() != null ? field.getMozname() : field.getMoname();

            // 跳过自动当前时间字段
            if ("自动当前时间".equals(controlType)) {
                continue;
            }

            // 跳过第一个int类型字段（通常是主键）
            if (i == 0 && "add".equals(action) && field.getMotype().toLowerCase().contains("int")) {
                continue;
            }

            // 收集必填字段
            if ("1".equals(field.getMoyz())) {
                requiredFields.add(field);
            }

            // 收集邮箱字段
            if (fieldComment.contains("邮箱") || fieldComment.contains("邮件")) {
                emailFields.add(field);
            }

            // 收集手机号字段
            if (fieldComment.contains("手机") || fieldComment.contains("电话") || fieldComment.contains("联系方式")) {
                phoneFields.add(field);
            }
        }

        // 生成变量声明
        if (!requiredFields.isEmpty() || !emailFields.isEmpty() || !phoneFields.isEmpty()) {
            validation.append("                // 获取表单字段值\n");

            // 为所有需要验证的字段生成变量声明
            Set<String> allFields = new HashSet<>();
            requiredFields.forEach(field -> allFields.add(field.getMoname()));
            emailFields.forEach(field -> allFields.add(field.getMoname()));
            phoneFields.forEach(field -> allFields.add(field.getMoname()));

            for (String fieldName : allFields) {
                validation.append("                var ").append(fieldName).append(" = $('#").append(fieldName).append("').val().trim();\n");
            }
            validation.append("                \n");
        }

        // 生成非空验证
        if (!requiredFields.isEmpty()) {
            validation.append("                // 非空验证\n");
            for (Mores field : requiredFields) {
                String fieldName = field.getMoname();
                String fieldComment = field.getMozname() != null ? field.getMozname() : fieldName;
                validation.append("                if (!").append(fieldName).append(") {\n");
                validation.append("                    CommonUtils.showToast('请输入").append(fieldComment).append("', 'error');\n");
                validation.append("                    return;\n");
                validation.append("                }\n");
            }
            validation.append("                \n");
        }

        // 生成邮箱格式验证
        if (!emailFields.isEmpty()) {
            validation.append("                // 邮箱格式验证\n");
            for (Mores field : emailFields) {
                String fieldName = field.getMoname();
                String fieldComment = field.getMozname() != null ? field.getMozname() : fieldName;
                validation.append("                if (").append(fieldName).append(" && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(").append(fieldName).append(")) {\n");
                validation.append("                    CommonUtils.showToast('").append(fieldComment).append("格式不正确', 'error');\n");
                validation.append("                    return;\n");
                validation.append("                }\n");
            }
            validation.append("                \n");
        }

        // 生成手机号格式验证
        if (!phoneFields.isEmpty()) {
            validation.append("                // 手机号格式验证\n");
            for (Mores field : phoneFields) {
                String fieldName = field.getMoname();
                String fieldComment = field.getMozname() != null ? field.getMozname() : fieldName;
                validation.append("                if (").append(fieldName).append(" && !/^1[3-9]\\d{9}$/.test(").append(fieldName).append(")) {\n");
                validation.append("                    CommonUtils.showToast('").append(fieldComment).append("格式不正确，请输入11位手机号', 'error');\n");
                validation.append("                    return;\n");
                validation.append("                }\n");
            }
            validation.append("                \n");
        }

        return validation.toString();
    }

    /**
     * 生成验证规则（已移至head.html中的通用方法）
     */
    private String generateValidationRules(List<Mores> fields) {
        // 验证功能已在head.html中的CommonUtils.validateForm方法中实现
        // 这里返回空字符串，保持接口兼容性
        return "";
    }

    /**
     * 生成数据加载脚本
     */
    private String generateLoadScript(List<Mores> fields, String tableName) {
        StringBuilder script = new StringBuilder();
        String entityNameLower = toCamelCase(tableName);

        script.append("        <script>\n");
        script.append("        $(document).ready(function() {\n");
        script.append("            // 页面加载时的初始化操作\n");
        script.append("            console.log('").append(tableName).append(" 页面加载完成');\n");
        script.append("        });\n");
        script.append("        </script>\n");

        return script.toString();
    }

    /**
     * 生成分页脚本
     */
    private String generatePaginationScript(String tableName) {
        StringBuilder script = new StringBuilder();

        script.append("        <script>\n");
        script.append("        // 分页功能\n");
        script.append("        function goToPage(page) {\n");
        script.append("            var url = new URL(window.location);\n");
        script.append("            url.searchParams.set('page', page);\n");
        script.append("            window.location.href = url.toString();\n");
        script.append("        }\n");
        script.append("        \n");
        script.append("        // 图片预览函数\n");
        script.append("        function previewImage(src) {\n");
        script.append("            var modal = '<div class=\"modal fade\" id=\"imagePreviewModal\" tabindex=\"-1\">' +\n");
        script.append("                       '<div class=\"modal-dialog modal-lg modal-dialog-centered\">' +\n");
        script.append("                       '<div class=\"modal-content\">' +\n");
        script.append("                       '<div class=\"modal-header\">' +\n");
        script.append("                       '<h5 class=\"modal-title\">图片预览</h5>' +\n");
        script.append("                       '<button type=\"button\" class=\"btn-close\" data-bs-dismiss=\"modal\"></button>' +\n");
        script.append("                       '</div>' +\n");
        script.append("                       '<div class=\"modal-body text-center\">' +\n");
        script.append("                       '<img src=\"' + src + '\" class=\"img-fluid\" style=\"max-height: 70vh;\">' +\n");
        script.append("                       '</div>' +\n");
        script.append("                       '</div></div></div>';\n");
        script.append("            \n");
        script.append("            $('body').append(modal);\n");
        script.append("            var modalInstance = new bootstrap.Modal(document.getElementById('imagePreviewModal'));\n");
        script.append("            modalInstance.show();\n");
        script.append("            \n");
        script.append("            $('#imagePreviewModal').on('hidden.bs.modal', function() {\n");
        script.append("                $(this).remove();\n");
        script.append("            });\n");
        script.append("        }\n");
        script.append("        </script>\n");

        return script.toString();
    }

    /**
     * 生成删除脚本
     */
    private String generateDeleteScript(String tableName) {
        StringBuilder script = new StringBuilder();
        String entityNameLower = toCamelCase(tableName);

        script.append("        <script>\n");
        script.append("        function confirmDelete() {\n");
        script.append("            CommonUtils.confirmDelete('确定要删除这条记录吗？此操作不可恢复！', function() {\n");
        script.append("                CommonUtils.submitForm('#deleteForm', \n");
        script.append("                    '/").append(entityNameLower).append("Delete', \n");
        script.append("                    function(response) {\n");
        script.append("                        // 成功回调\n");
        script.append("                        setTimeout(function() {\n");
        script.append("                            window.location.href = '/").append(entityNameLower).append("List';\n");
        script.append("                        }, 1500);\n");
        script.append("                    },\n");
        script.append("                    function(error) {\n");
        script.append("                        // 错误回调\n");
        script.append("                        console.error('删除失败:', error);\n");
        script.append("                    }\n");
        script.append("                );\n");
        script.append("            });\n");
        script.append("        }\n");
        script.append("        </script>\n");

        return script.toString();
    }

    /**
     * 获取主键字段名
     */
    private String getPrimaryKeyField(String tableName) {
        // 简单实现，实际应该从数据库查询
        return toCamelCase(tableName.toLowerCase().endsWith("s") ? tableName.substring(0, tableName.length() - 1) + "Id" : tableName + "Id");
    }

    /**
     * 生成修改密码页面
     */
    public String generatePasswordChangePage() {
        StringBuilder content = new StringBuilder();

        // 页面基本结构
        content.append("<!DOCTYPE html>\n");
        content.append("<html lang=\"zh-CN\">\n\n");
        content.append("<head>\n");
        content.append("    <meta charset=\"UTF-8\">\n");
        content.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        content.append("<head th:replace=\"@{/admin/head.html}\"></head>\n");
        content.append("  \n");
        content.append("</head>\n\n");

        content.append("<body>\n");
        content.append("    <div class=\"content-area\" style=\"height: 100%; padding: 20px;\">\n");
        content.append("        <h4 class=\"page-title\">修改密码</h4>\n\n");
        content.append("        <div>\n");
        content.append("        <div class=\"container-fluid\">\n");
        content.append("    <div class=\"card\">\n");
        content.append("        <div class=\"card-header\">\n");
        content.append("            <h5 class=\"card-title mb-0\">修改密码</h5>\n");
        content.append("        </div>\n");
        content.append("        <div class=\"card-body\">\n");
        content.append("            <form id=\"passwordForm\" method=\"post\">\n");

        // 原密码字段
        content.append("                <div class=\"form-group row mb-3\">\n");
        content.append("                    <label for=\"txt_pwd\" class=\"col-sm-3 col-form-label\">原密码</label>\n");
        content.append("                    <div class=\"col-sm-9\">\n");
        content.append("                        <input type=\"password\" class=\"form-control\" id=\"txt_pwd\" name=\"txt_pwd\" >\n");
        content.append("                    </div>\n");
        content.append("                </div>\n");

        // 新密码字段
        content.append("                <div class=\"form-group row mb-3\">\n");
        content.append("                    <label for=\"txt_pwd2\" class=\"col-sm-3 col-form-label\">新密码</label>\n");
        content.append("                    <div class=\"col-sm-9\">\n");
        content.append("                        <input type=\"password\" class=\"form-control\" id=\"txt_pwd2\" name=\"txt_pwd2\" >\n");
        content.append("                    </div>\n");
        content.append("                </div>\n");

        // 确认密码字段
        content.append("                <div class=\"form-group row mb-3\">\n");
        content.append("                    <label for=\"txt_pwd3\" class=\"col-sm-3 col-form-label\">确认密码</label>\n");
        content.append("                    <div class=\"col-sm-9\">\n");
        content.append("                        <input type=\"password\" class=\"form-control\" id=\"txt_pwd3\" name=\"txt_pwd3\" >\n");
        content.append("                    </div>\n");
        content.append("                </div>\n");

        // 按钮区域
        content.append("                <div class=\"form-group row\">\n");
        content.append("                    <div class=\"col-sm-9 offset-sm-3\">\n");
        content.append("                        <button type=\"submit\" class=\"btn btn-primary\">\n");
        content.append("                            <i class=\"bi bi-floppy-fill\"></i> 保存\n");
        content.append("                        </button>\n");
        content.append("                        <a href=\"javascript:history.back()\" class=\"btn btn-secondary ml-2\">\n");
        content.append("                            <i class=\"bi bi-arrow-left\"></i> 返回\n");
        content.append("                        </a>\n");
        content.append("                    </div>\n");
        content.append("                </div>\n");
        content.append("            </form>\n");
        content.append("        </div>\n");
        content.append("    </div>\n");
        content.append("</div>\n");

        // JavaScript脚本
        content.append("        <script>\n");
        content.append("        $(document).ready(function() {\n");
        content.append("            $('#passwordForm').on('submit', function(e) {\n");
        content.append("                e.preventDefault();\n");
        content.append("                \n");
        content.append("                // 非空验证\n");
        content.append("                var pwd1 = $('#txt_pwd').val().trim();\n");
        content.append("                var pwd2 = $('#txt_pwd2').val().trim();\n");
        content.append("                var pwd3 = $('#txt_pwd3').val().trim();\n");
        content.append("                \n");
        content.append("                if (!pwd1) {\n");
        content.append("                    CommonUtils.showToast('请输入原密码', 'error');\n");
        content.append("                    return;\n");
        content.append("                }\n");
        content.append("                if (!pwd2) {\n");
        content.append("                    CommonUtils.showToast('请输入新密码', 'error');\n");
        content.append("                    return;\n");
        content.append("                }\n");
        content.append("                if (!pwd3) {\n");
        content.append("                    CommonUtils.showToast('请输入确认密码', 'error');\n");
        content.append("                    return;\n");
        content.append("                }\n");
        content.append("                \n");
        content.append("                // 使用通用表单提交方法\n");
        content.append("                CommonUtils.submitForm('#passwordForm', \n");
        content.append("                    '/adminPass', \n");
        content.append("                    function(response) {\n");
        content.append("                        // 成功回调\n");
        content.append("                        CommonUtils.showToast('密码修改成功！', 'success');\n");
        content.append("                        setTimeout(function() {\n");
        content.append("                            // 清空表单\n");
        content.append("                            $('#passwordForm')[0].reset();\n");
        content.append("                        }, 1500);\n");
        content.append("                    },\n");
        content.append("                    function(error) {\n");
        content.append("                             console.error('提交失败:', error);\n");
        content.append("                    }\n");
        content.append("                );\n");
        content.append("            });\n");
        content.append("        });\n");
        content.append("        </script>\n\n");
        content.append("        </div>\n");
        content.append("    </div>\n\n");
        content.append("    <!-- Bootstrap JS -->\n");
        content.append("</body>\n\n");
        content.append("</html>");

        return content.toString();
    }

    /**
     * 生成修改密码页面并返回文件列表
     */
    public Map<String, String> generatePasswordChangePageFiles() {
        Map<String, String> files = new HashMap<>();

        try {
            // 生成修改密码页面内容
            String passwordPageContent = generatePasswordChangePage();

            // 添加到文件列表中
            files.put("src/main/resources/templates/admin/admin/pass.html", passwordPageContent);

            System.out.println("生成修改密码页面: admin/admin/pass.html");

        } catch (Exception e) {
            System.err.println("生成修改密码页面失败: " + e.getMessage());
            e.printStackTrace();
        }

        return files;
    }

    /**
     * 生成注册页面
     */
    public String generateRegistrationPage(List<Mores> fields, String tableName, String tableComment) {
        StringBuilder content = new StringBuilder();
        String entityNameLower = toCamelCase(tableName);

        // 页面基本结构
        content.append("<!DOCTYPE html>\n");
        content.append("<html lang=\"zh-CN\">\n\n");
        content.append("<head>\n");
        content.append("    <meta charset=\"UTF-8\">\n");
        content.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        content.append("<head th:replace=\"@{/admin/head.html}\"></head>\n");
        content.append("  \n");
        content.append("</head>\n\n");

        content.append("<body>\n");
        content.append("    <div class=\"content-area\" style=\"height: 100%; padding: 20px;\">\n");
        content.append("        <h4 class=\"page-title\">").append(tableComment).append("注册</h4>\n\n");
        content.append("        <div>\n");
        content.append("        <div class=\"container-fluid\">\n");
        content.append("    <div class=\"card\">\n");
        content.append("        <div class=\"card-header\">\n");
        content.append("            <h5 class=\"card-title mb-0\">").append(tableComment).append("注册</h5>\n");
        content.append("        </div>\n");
        content.append("        <div class=\"card-body\">\n");
        content.append("            <form id=\"").append(entityNameLower).append("RegForm\" method=\"post\">\n");

        // 生成表单字段（排除主键），确认密码字段会在密码字段后面自动添加
        content.append(generateFormFields(fields, "register"));

        // 按钮区域
        content.append("                <div class=\"form-group row\">\n");
        content.append("                    <div class=\"col-sm-9 offset-sm-3\">\n");
        content.append("                        <button type=\"submit\" class=\"btn btn-primary\">\n");
        content.append("                            <i class=\"bi bi-floppy-fill\"></i> 注册\n");
        content.append("                        </button>\n");
        content.append("                        <a href=\"/qtologin\" class=\"btn btn-secondary ml-2\">\n");
        content.append("                            <i class=\"bi bi-arrow-left\"></i> 返回登录\n");
        content.append("                        </a>\n");
        content.append("                    </div>\n");
        content.append("                </div>\n");
        content.append("            </form>\n");
        content.append("        </div>\n");
        content.append("    </div>\n");
        content.append("</div>\n");

        // JavaScript脚本
        content.append("        <script>\n");
        content.append("        $(document).ready(function() {\n");
        content.append("            $('#").append(entityNameLower).append("RegForm').on('submit', function(e) {\n");
        content.append("                e.preventDefault();\n");
        content.append("                \n");
        content.append("                // 使用通用表单提交方法\n");
        content.append("                CommonUtils.submitForm('#").append(entityNameLower).append("RegForm', \n");
        content.append("                    '/").append(entityNameLower).append("Reg', \n");
        content.append("                    function(response) {\n");
        content.append("                        // 成功回调\n");
        content.append("                        CommonUtils.showToast('注册成功！', 'success');\n");
        content.append("                        setTimeout(function() {\n");
        content.append("                            window.location.href = '/qtologin';\n");
        content.append("                        }, 1500);\n");
        content.append("                    },\n");
        content.append("                    function(error) {\n");
        content.append("                        // 错误回调\n");
        content.append("                        if (typeof error === 'string') {\n");
        content.append("                            if (error.indexOf('用户名已存在') !== -1) {\n");
        content.append("                                CommonUtils.showToast('该用户名已存在，请重新输入', 'error');\n");
        content.append("                            } else if (error.indexOf('两次密码输入不一致') !== -1) {\n");
        content.append("                                CommonUtils.showToast('两次密码输入不一致，请重新输入', 'error');\n");
        content.append("                            } else {\n");
        content.append("                                CommonUtils.showToast('注册失败', 'error');\n");
        content.append("                            }\n");
        content.append("                        } else {\n");
        content.append("                            CommonUtils.showToast('网络错误，请重试', 'error');\n");
        content.append("                        }\n");
        content.append("                    }\n");
        content.append("                );\n");
        content.append("            });\n");
        content.append("        });\n");
        content.append("        </script>\n\n");
        content.append("        </div>\n");
        content.append("    </div>\n\n");
        content.append("    <!-- Bootstrap JS -->\n");
        content.append("</body>\n\n");
        content.append("</html>");

        return content.toString();
    }

    /**
     * 生成个人信息页面
     */
    public String generatePersonalInfoPage(List<Mores> fields, String tableName, String tableComment) {
        StringBuilder content = new StringBuilder();
        String entityNameLower = toCamelCase(tableName);

        // 根据第一个字段类型决定忽略的字段数量
        int skipFields = 0;
        if (fields != null && !fields.isEmpty()) {
            String firstFieldType = fields.get(0).getMotype();
            if (firstFieldType != null) {
                if (firstFieldType.toLowerCase().contains("int")) {
                    skipFields = 3; // 如果第1个字段是int就忽略前3个字段
                } else if (firstFieldType.toLowerCase().contains("varchar")) {
                    skipFields = 2; // 如果第1个字段是varchar，就忽略前2个字段
                }
            }
        }

        // 页面基本结构
        content.append("<!DOCTYPE html>\n");
        content.append("<html lang=\"zh-CN\">\n\n");
        content.append("<head>\n");
        content.append("    <meta charset=\"UTF-8\">\n");
        content.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        content.append("<head th:replace=\"@{/admin/head.html}\"></head>\n");
        content.append("  \n");
        content.append("</head>\n\n");

        content.append("<body>\n");
        content.append("    <div class=\"content-area\" style=\"height: 100%; padding: 20px;\">\n");
        content.append("        <h4 class=\"page-title\">修改个人信息</h4>\n\n");
        content.append("        <div>\n");
        content.append("        <div class=\"container-fluid\">\n");
        content.append("    <div class=\"card\">\n");
        content.append("        <div class=\"card-header\">\n");
        content.append("            <h5 class=\"card-title mb-0\">修改个人信息</h5>\n");
        content.append("        </div>\n");
        content.append("        <div class=\"card-body\">\n");
        content.append("            <form id=\"").append(entityNameLower).append("InfoForm\" method=\"post\">\n");

        // 生成表单字段（根据规则忽略前面的字段）
        content.append(generatePersonalInfoFormFields(fields, skipFields));

        // 按钮区域
        content.append("                <div class=\"form-group row\">\n");
        content.append("                    <div class=\"col-sm-9 offset-sm-3\">\n");
        content.append("                        <button type=\"submit\" class=\"btn btn-primary\">\n");
        content.append("                            <i class=\"bi bi-floppy-fill\"></i> 保存\n");
        content.append("                        </button>\n");
        content.append("                        <a href=\"javascript:history.back()\" class=\"btn btn-secondary ml-2\">\n");
        content.append("                            <i class=\"bi bi-arrow-left\"></i> 返回\n");
        content.append("                        </a>\n");
        content.append("                    </div>\n");
        content.append("                </div>\n");
        content.append("            </form>\n");
        content.append("        </div>\n");
        content.append("    </div>\n");
        content.append("</div>\n");

        // JavaScript脚本
        content.append("        <script>\n");
        content.append("        $(document).ready(function() {\n");
        content.append("            $('#").append(entityNameLower).append("InfoForm').on('submit', function(e) {\n");
        content.append("                e.preventDefault();\n");
        content.append("                \n");
        content.append("                // 使用通用表单提交方法\n");
        content.append("                CommonUtils.submitForm('#").append(entityNameLower).append("InfoForm', \n");
        content.append("                    '/").append(entityNameLower).append("Info', \n");
        content.append("                    function(response) {\n");
        content.append("                        // 成功回调\n");
        content.append("                        CommonUtils.showToast('个人信息修改成功！', 'success');\n");
        content.append("                    },\n");
        content.append("                    function(error) {\n");
        content.append("                        // 错误回调\n");
        content.append("                        CommonUtils.showToast('修改失败，请重试', 'error');\n");
        content.append("                    }\n");
        content.append("                );\n");
        content.append("            });\n");
        content.append("        });\n");
        content.append("        </script>\n\n");
        content.append("        </div>\n");
        content.append("    </div>\n\n");
        content.append("    <!-- Bootstrap JS -->\n");
        content.append("</body>\n\n");
        content.append("</html>");

        return content.toString();
    }

    /**
     * 获取密码字段名称
     */
    private String getPasswordFieldName(List<Mores> fields) {
        if (fields != null) {
            for (Mores field : fields) {
                String chineseName = field.getMozname();
                if (chineseName != null && chineseName.contains("密码")) {
                    return field.getMoname();
                }
            }
        }
        return null;
    }

    /**
     * 生成个人信息表单字段（根据规则忽略前面的字段）
     */
    private String generatePersonalInfoFormFields(List<Mores> fields, int skipFields) {
        StringBuilder formFields = new StringBuilder();

        if (fields != null && fields.size() > skipFields) {
            // 跳过前面的字段
            List<Mores> fieldsToShow = fields.subList(skipFields, fields.size());

            for (Mores field : fieldsToShow) {
                String fieldName = field.getMoname();
                String fieldComment = field.getMozname() != null ? field.getMozname() : fieldName;
                String fieldType = field.getMotype();
                String controlType = field.getMoflag() != null ? field.getMoflag() : "文本框";

                // 跳过自动时间字段
                if ("自动当前时间".equals(field.getMoflag())) {
                    continue;
                }

                formFields.append("                <div class=\"form-group row mb-3\">\n");
                formFields.append("                    <label for=\"").append(fieldName).append("\" class=\"col-sm-3 col-form-label\">").append(fieldComment).append("</label>\n");
                formFields.append("                    <div class=\"col-sm-9\">\n");

                // 根据控件类型生成不同的输入框
                if ("密码框".equals(controlType) || (fieldComment != null && fieldComment.contains("密码"))) {
                    formFields.append("                        <input type=\"password\" class=\"form-control\" id=\"").append(fieldName).append("\" name=\"").append(fieldName).append("\" th:value=\"${item.").append(fieldName).append("}\">\n");
                } else if ("文本域".equals(controlType)) {
                    formFields.append("                        <textarea class=\"form-control\" id=\"").append(fieldName).append("\" name=\"").append(fieldName).append("\" rows=\"3\" th:text=\"${item.").append(fieldName).append("}\"></textarea>\n");
                } else if ("下拉框".equals(controlType)) {
                    formFields.append("                        <select class=\"form-control\" id=\"").append(fieldName).append("\" name=\"").append(fieldName).append("\">\n");
                    formFields.append("                            <option value=\"\">请选择</option>\n");
                    formFields.append("                            <!-- 这里需要根据实际需求添加选项 -->\n");
                    formFields.append("                        </select>\n");
                } else if ("日期框".equals(controlType)) {
                    formFields.append("                        <input type=\"date\" class=\"form-control\" id=\"").append(fieldName).append("\" name=\"").append(fieldName).append("\" th:value=\"${item.").append(fieldName).append("}\">\n");
                } else if ("时间框".equals(controlType)) {
                    formFields.append("                        <input type=\"datetime-local\" class=\"form-control\" id=\"").append(fieldName).append("\" name=\"").append(fieldName).append("\" th:value=\"${item.").append(fieldName).append("}\">\n");
                } else {
                    // 默认文本框
                    formFields.append("                        <input type=\"text\" class=\"form-control\" id=\"").append(fieldName).append("\" name=\"").append(fieldName).append("\" th:value=\"${item.").append(fieldName).append("}\">\n");
                }

                formFields.append("                    </div>\n");
                formFields.append("                </div>\n");
            }
        }

        return formFields.toString();
    }

    /**
     * 根据表功能配置生成注册和个人信息页面
     */
    public Map<String, String> generateRegistrationAndPersonalInfoPages(Integer projectId) {
        Map<String, String> files = new HashMap<>();

        try {
            // 获取项目的所有表
            Tables queryTables = new Tables();
            queryTables.setPid(projectId);
            List<Tables> tablesList = tablesService.queryTablesList(queryTables, null);

            if (tablesList != null) {
                for (Tables table : tablesList) {
                    Map<String, Boolean> tableFunctions = parseTableFunctions(table.getTgn());
                    String tableName = table.getTname();
                    String tableComment = table.getTword() != null ? table.getTword() : table.getTname();

                    // 获取表的字段信息
                    Mores queryMores = new Mores();
                    queryMores.setTid(table.getTid());
                    List<Mores> fieldsList = moresService.queryMoresList(queryMores, null);

                    // 如果选中了后台注册，生成注册页面
                    if (tableFunctions.getOrDefault("backendRegister", false)) {
                        String registrationPage = generateRegistrationPage(fieldsList, tableName, tableComment);
                        files.put("src/main/resources/templates/" + tableName + "_Reg.html", registrationPage);
                        System.out.println("生成注册页面: " + tableName + "_Reg.html");
                    }

                    // 如果选中了后台个人信息，生成个人信息页面
                    if (tableFunctions.getOrDefault("backendProfile", false)) {
                        String personalInfoPage = generatePersonalInfoPage(fieldsList, tableName, tableComment);
                        files.put("src/main/resources/templates/admin/" + tableName + "/" + tableName + "_Info.html", personalInfoPage);
                        System.out.println("生成个人信息页面: admin/" + tableName + "/" + tableName + "_Info.html");
                    }
                }
            }

        } catch (Exception e) {
            System.err.println("生成注册和个人信息页面失败: " + e.getMessage());
            e.printStackTrace();
        }

        return files;
    }

    /**
     * 解析表功能配置
     */
    private Map<String, Boolean> parseTableFunctions(String tgn) {
        Map<String, Boolean> functions = new HashMap<>();

        // 默认值
        functions.put("backendLogin", false);
        functions.put("backendPassword", false);
        functions.put("backendRegister", false);
        functions.put("backendProfile", false); // 修改为 backendProfile
        functions.put("backendAdd", true);
        functions.put("backendEdit", true);
        functions.put("backendDelete", true);
        functions.put("backendDetail", true);
        functions.put("backendList", true);

        if (tgn != null && !tgn.trim().isEmpty()) {
            try {
                // 检查是否是JSON格式
                if (tgn.trim().startsWith("{") && tgn.trim().endsWith("}")) {
                    // 解析JSON格式的配置
                    parseJsonTableFunctions(tgn, functions);
                } else {
                    // 兼容旧的数字编码格式
                    parseNumericTableFunctions(tgn, functions);
                }
            } catch (Exception e) {
                System.err.println("解析表功能配置失败: " + e.getMessage());
                // 使用默认值
            }
        }

        return functions;
    }

    /**
     * 解析JSON格式的表功能配置
     */
    private void parseJsonTableFunctions(String tgn, Map<String, Boolean> functions) {
        try {
            // 简单的JSON解析（避免引入额外依赖）
            String json = tgn.trim();

            // 提取各个字段的值
            functions.put("backendLogin", extractBooleanFromJson(json, "backendLogin"));
            functions.put("backendPassword", extractBooleanFromJson(json, "backendPassword"));
            functions.put("backendRegister", extractBooleanFromJson(json, "backendRegister"));
            functions.put("backendProfile", extractBooleanFromJson(json, "backendProfile"));
            functions.put("backendAdd", extractBooleanFromJson(json, "backendAdd"));
            functions.put("backendEdit", extractBooleanFromJson(json, "backendEdit"));
            functions.put("backendDelete", extractBooleanFromJson(json, "backendDelete"));
            functions.put("backendDetail", extractBooleanFromJson(json, "backendDetail"));
            functions.put("backendList", extractBooleanFromJson(json, "backendList"));

        } catch (Exception e) {
            System.err.println("解析JSON表功能配置失败: " + e.getMessage());
        }
    }

    /**
     * 从JSON字符串中提取布尔值
     */
    private boolean extractBooleanFromJson(String json, String key) {
        try {
            String pattern = "\"" + key + "\"\\s*:\\s*(true|false)";
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(json);
            if (m.find()) {
                return Boolean.parseBoolean(m.group(1));
            }
        } catch (Exception e) {
            System.err.println("提取JSON字段失败: " + key + ", " + e.getMessage());
        }
        return false; // 默认值
    }

    /**
     * 解析数字编码格式的表功能配置（兼容旧格式）
     */
    private void parseNumericTableFunctions(String tgn, Map<String, Boolean> functions) {
        String[] parts = tgn.split(",");
        for (String part : parts) {
            String trimmedPart = part.trim();
            switch (trimmedPart) {
                case "1":
                    functions.put("backendLogin", true);
                    break;
                case "2":
                    functions.put("backendPassword", true);
                    break;
                case "3":
                    functions.put("backendRegister", true);
                    break;
                case "4":
                    functions.put("backendProfile", true);
                    break;
                case "5":
                    functions.put("backendAdd", true);
                    break;
                case "6":
                    functions.put("backendEdit", true);
                    break;
                case "7":
                    functions.put("backendDelete", true);
                    break;
                case "8":
                    functions.put("backendDetail", true);
                    break;
                case "9":
                    functions.put("backendList", true);
                    break;
            }
        }
    }

    /**
     * 生成通用样式（已移至head.html中）
     */
    private String generateCommonStyles() {
        // 样式已在head.html中定义，这里返回空字符串保持接口兼容性
        return "";
    }
}
