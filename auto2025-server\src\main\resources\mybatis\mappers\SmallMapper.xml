<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mapper.SmallMapper">
	<select id="findSmallList"  resultType="Small">
		select * from small 
	</select>
	
	<select id="query" parameterType="java.util.Map" resultType="Small">
	    select  *  
        from small a  left join big b on a.bid=b.bid  	
		<where>
      		<if test="sid != null and sid !=0 ">
		    and a.sid = #{sid}
		</if>
		<if test="bid != null and bid !=0 ">
		    and a.bid = #{bid}
		</if>
		<if test="sname != null and sname != ''">
		    and a.sname = #{sname}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>

    order by ${sort} sid desc

    <if test="page">
			limit #{offset} ,#{pageSize}
		</if>
	</select>	
	
	<select id="getCount" parameterType="java.util.Map" resultType="Int">
		select count(0) from small a  left join big b on a.bid=b.bid  
		<where>
      		<if test="sid != null and sid !=0 ">
		    and a.sid = #{sid}
		</if>
		<if test="bid != null and bid !=0 ">
		    and a.bid = #{bid}
		</if>
		<if test="sname != null and sname != ''">
		    and a.sname = #{sname}
		</if>
		<if test="condition != null and condition != ''">
		    ${condition} 
		</if>

    </where>
	</select>	
	
	<select id="querySmallById" parameterType="int" resultType="Small">
    select  *  
     from small a  left join big b on a.bid=b.bid  	 where a.sid=#{value}
  </select>
 
	<insert id="insertSmall" useGeneratedKeys="true" keyProperty="sid" parameterType="Small">
    insert into small
    (bid,sname,memo1,memo2,memo3,memo4,by1,by2,by3,by4,by5,by6,by7,by8,by9,by10)
    values
    (#{bid},#{sname},#{memo1},#{memo2},#{memo3},#{memo4},#{by1},#{by2},#{by3},#{by4},#{by5},#{by6},#{by7},#{by8},#{by9},#{by10});
  </insert>
	
	<update id="updateSmall" parameterType="Small" >
    update small 
    <set>
		<if test="bid != null ">
		    bid = #{bid},
		</if>
		<if test="sname != null and sname != ''">
		    sname = #{sname},
		</if>
		<if test="memo1 != null and memo1 != ''">
		    memo1 = #{memo1},
		</if>
		<if test="memo2 != null and memo2 != ''">
		    memo2 = #{memo2},
		</if>
		<if test="memo3 != null and memo3 != ''">
		    memo3 = #{memo3},
		</if>
		<if test="memo4 != null and memo4 != ''">
		    memo4 = #{memo4},
		</if>
		<if test="by1 != null and by1 != ''">
		    by1 = #{by1},
		</if>
		<if test="by2 != null and by2 != ''">
		    by2 = #{by2},
		</if>
		<if test="by3 != null and by3 != ''">
		    by3 = #{by3},
		</if>
		<if test="by4 != null and by4 != ''">
		    by4 = #{by4},
		</if>
		<if test="by5 != null and by5 != ''">
		    by5 = #{by5},
		</if>
		<if test="by6 != null and by6 != ''">
		    by6 = #{by6},
		</if>
		<if test="by7 != null and by7 != ''">
		    by7 = #{by7},
		</if>
		<if test="by8 != null and by8 != ''">
		    by8 = #{by8},
		</if>
		<if test="by9 != null and by9 != ''">
		    by9 = #{by9},
		</if>
		<if test="by10 != null and by10 != ''">
		    by10 = #{by10},
		</if>


    </set>
   <where> 
    <if test="condition != null and condition != ''">
      ${condition}
    </if>
    <if test="sid != null or sid != ''">
      sid=#{sid}
    </if>
   </where>     
  </update>	
 
	
	<delete id="deleteSmall" parameterType="int">
    delete from  small where sid=#{value}
  </delete>

	
	
</mapper>

 
