﻿<template>
  <div style="width: 100%;line-height: 30px;text-align: left;">
    <el-form :model="formData" label-width="20%" ref="formDataRef" :rules="addrules" align="left">
      <el-form-item label="大类" prop="bid">
        <el-select v-model="formData.bid" placeholder="请选择" size="small">
          <el-option v-for="item in bigList" :key="item.bid" :label="item.bname" :value="item.bid"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="小类名称" prop="sname">
        <el-input v-model="formData.sname" placeholder="小类名称" style="width:50%;"></el-input>
      </el-form-item>

      <el-tabs v-model="activeTab">
        <el-tab-pane label="内容1" name="memo1">
          <el-form-item label="内容1" prop="memo1">
            <el-input type="textarea" :rows="25" v-model="formData.memo1" placeholder="内容1" size="small"></el-input>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="内容2" name="memo2">
          <el-form-item label="内容2" prop="memo2">
            <el-input type="textarea" :rows="25" v-model="formData.memo2" placeholder="内容2" size="small"></el-input>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="内容3" name="memo3">
          <el-form-item label="内容3" prop="memo3">
            <el-input type="textarea" :rows="25" v-model="formData.memo3" placeholder="内容3" size="small"></el-input>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="内容4" name="memo4">
          <el-form-item label="内容4" prop="memo4">
            <WangEditor ref="wangEditorRef" v-model="formData.memo4" :config="editorConfig" :isClear="isClear"
              @change="editorChange"></WangEditor>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="备用1" name="by1">
          <el-form-item label="备用1" prop="by1">
            <el-input type="textarea" :rows="25" v-model="formData.by1" placeholder="备用1" size="small"></el-input>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="备用2" name="by2">
          <el-form-item label="备用2" prop="by2">
            <el-input type="textarea" :rows="25" v-model="formData.by2" placeholder="备用2" size="small"></el-input>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="备用3" name="by3">
          <el-form-item label="备用3" prop="by3">
            <el-input type="textarea" :rows="25" v-model="formData.by3" placeholder="备用3" size="small"></el-input>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="备用4" name="by4">
          <el-form-item label="备用4" prop="by4">
            <el-input type="textarea" :rows="25" v-model="formData.by4" placeholder="备用4" size="small"></el-input>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="备用5" name="by5">
          <el-form-item label="备用5" prop="by5">
            <el-input type="textarea" :rows="25" v-model="formData.by5" placeholder="备用5" size="small"></el-input>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="备用6" name="by6">
          <el-form-item label="备用6" prop="by6">
            <el-input type="textarea" :rows="25" v-model="formData.by6" placeholder="备用6" size="small"></el-input>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="备用7" name="by7">
          <el-form-item label="备用7" prop="by7">
            <el-input type="textarea" :rows="25" v-model="formData.by7" placeholder="备用7" size="small"></el-input>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="备用8" name="by8">
          <el-form-item label="备用8" prop="by8">
            <el-input type="textarea" :rows="25" v-model="formData.by8" placeholder="备用8" size="small"></el-input>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="备用9" name="by9">
          <el-form-item label="备用9" prop="by9">
            <el-input type="textarea" :rows="25" v-model="formData.by9" placeholder="备用9" size="small"></el-input>
          </el-form-item>
        </el-tab-pane>

        <el-tab-pane label="备用10" name="by10">
          <el-form-item label="备用10" prop="by10">
            <el-input type="textarea" :rows="25" v-model="formData.by10" placeholder="备用10" size="small"></el-input>
          </el-form-item>
        </el-tab-pane>



      </el-tabs>

      <el-form-item>
        <el-button type="primary" size="small" @click="save" :loading="btnLoading" icon="el-icon-upload">提 交</el-button>
        <el-button type="info" size="small" @click="goBack" icon="el-icon-back">返 回</el-button>
      </el-form-item>
    </el-form>


  </div>
</template>

<script>
  import request, { base } from "../../../../utils/http";
  import WangEditor from "../../../components/WangEditor";
  export default {
    name: 'SmallAdd',
    components: {
      WangEditor,
    },
    data() {
      return {
        activeTab: 'memo1', // 默认激活第一个标签页
        uploadVisible: false,
        btnLoading: false, //保存按钮加载状态     
        formData: {}, //表单数据           
        addrules: {
          bid: [{ required: true, message: '请选择大类', trigger: 'onchange' }],
          sname: [{ required: true, message: '请输入小类名称', trigger: 'blur' },
          ],
        },

      };
    },
    mounted() {

      this.getbigList();
    },


    methods: {
      // 添加
      save() {
        this.$refs["formDataRef"].validate((valid) => { //验证表单
          if (valid) {
            let url = base + "/small/add";
            this.btnLoading = true;
            request.post(url, this.formData).then((res) => { //发送请求         
              if (res.code == 200) {
                this.$message({
                  message: "操作成功",
                  type: "success",
                  offset: 320,
                });
                this.$router.push({
                  path: "/SmallManage",
                });
              } else {
                this.$message({
                  message: res.msg,
                  type: "error",
                  offset: 320,
                });
              }
              this.btnLoading = false;
            });
          }

        });
      },

      // 返回
      goBack() {
        this.$router.push({
          path: "/SmallManage",
        });
      },


      getbigList() {
        let para = {};
        this.listLoading = true;
        let url = base + "/big/list?currentPage=1&pageSize=1000";
        request.post(url, para).then((res) => {
          this.bigList = res.resdata;
        });
      },


      // 富文本编辑器
      editorChange(val) {
        this.formData.memo4 = val;
      },

    },
  }

</script>
<style scoped>
</style>