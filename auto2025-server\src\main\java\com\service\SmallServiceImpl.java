package com.service;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mapper.SmallMapper;
import com.model.Small;
import com.util.PageBean;
@Service
public class SmallServiceImpl implements SmallService{
        
	@Autowired
	private SmallMapper smallMapper;

	//查询多条记录
	public List<Small> querySmallList(Small small,PageBean page) throws Exception {
		Map<String, Object> map =getQueryMap(small, page);
		
		List<Small> getSmall = smallMapper.query(map);
		
		return getSmall;
	}
	
	//得到记录总数
	@Override
	public int getCount(Small small) {
		Map<String, Object> map = getQueryMap(small, null);
		int count = smallMapper.getCount(map);
		return count;
	}
	
	private Map<String, Object> getQueryMap(Small small,PageBean page){
		Map<String, Object> map = new HashMap<String, Object>();
		if(small!=null){
			map.put("sid", small.getSid());
			map.put("bid", small.getBid());
			map.put("sname", small.getSname());
			map.put("memo1", small.getMemo1());
			map.put("memo2", small.getMemo2());
			map.put("memo3", small.getMemo3());
			map.put("memo4", small.getMemo4());
			map.put("by1", small.getBy1());
			map.put("by2", small.getBy2());
			map.put("by3", small.getBy3());
			map.put("by4", small.getBy4());
			map.put("by5", small.getBy5());
			map.put("by6", small.getBy6());
			map.put("by7", small.getBy7());
			map.put("by8", small.getBy8());
			map.put("by9", small.getBy9());
			map.put("by10", small.getBy10());
			map.put("sort", small.getSort());
			map.put("condition", small.getCondition());

		}
		PageBean.setPageMap(map, page);
		return map;
	}
		
	//添加
	public int insertSmall(Small small) throws Exception {
		return smallMapper.insertSmall(small);
	}

	//根据ID删除
	public int deleteSmall(int id) throws Exception {
		return smallMapper.deleteSmall(id);
	}

	//更新
	public int updateSmall(Small small) throws Exception {
		return smallMapper.updateSmall(small);
	}
	
	//根据ID得到对应的记录
	public Small querySmallById(int id) throws Exception {
		Small po =  smallMapper.querySmallById(id);
		return po;
	}
}

