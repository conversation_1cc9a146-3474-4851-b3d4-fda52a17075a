<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
<head th:replace="@{/admin/head.html}"></head>
  
</head>

<body>
    <div class="content-area" style="height: 100%; padding: 20px;">
        <h4 class="page-title">添加系统公告</h4>

        <div>
        <div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">添加系统公告</h5>
        </div>
        <div class="card-body">
            <form id="systemnoticeForm" method="post">
                <div class="form-group row mb-3">
                    <label for="title" class="col-sm-3 col-form-label">标题 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="title" name="title">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="content" class="col-sm-3 col-form-label">内容</label>
                    <div class="col-sm-9">
                        <textarea class="form-control" id="content" name="content" rows="3"></textarea>
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-9 offset-sm-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-floppy-fill"></i> 保存
                        </button>
                        <a href="/systemnoticeList" class="btn btn-secondary ml-2">
                            <i class="bi bi-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
        <script>
        $(document).ready(function() {
            $('#systemnoticeForm').on('submit', function(e) {
                e.preventDefault();
                
                // 非空验证
                var title = $('#title').val().trim();
                
                if (!title) {
                    CommonUtils.showToast('请输入标题', 'error');
                    return;
                }
                
                // 使用通用表单提交方法
                CommonUtils.submitForm('#systemnoticeForm', 
                    '/systemnoticeAdd', 
                    function(response) {
                        // 成功回调
                        CommonUtils.showToast('操作成功！', 'success');
                        setTimeout(function() {
                            window.location.href = '/systemnoticeList';
                        }, 1500);
                    },
                    function(error) {
                        // 错误回调
                        console.error('提交失败:', error);
                    }
                );
            });
        });
        </script>

        </div>
    </div>

    <!-- Bootstrap JS -->
</body>

</html>