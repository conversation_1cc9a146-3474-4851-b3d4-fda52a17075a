<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
<head th:replace="@{/admin/head.html}"></head>
  
</head>

<body>
    <div class="content-area" style="height: 100%; padding: 20px;">
        <h4 class="page-title">编辑管理员</h4>

        <div>
        <div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">编辑管理员</h5>
        </div>
        <div class="card-body">
            <form id="adminForm" method="post">
                        <input type="hidden" id="id" name="id" th:value="${item.id}">
                <div class="form-group row mb-3">
                    <label for="lname" class="col-sm-3 col-form-label">账号 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" id="lname" name="lname" th:value="${item.lname}">
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="password" class="col-sm-3 col-form-label">登录密码 <span class="required">*</span></label>
                    <div class="col-sm-9">
                        <input type="password" class="form-control" id="password" name="password" th:value="${item.password}">
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-9 offset-sm-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-floppy-fill"></i> 保存
                        </button>
                        <a href="/adminList" class="btn btn-secondary ml-2">
                            <i class="bi bi-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
        <script>
        $(document).ready(function() {
            $('#adminForm').on('submit', function(e) {
                e.preventDefault();
                
                // 非空验证
                var id = $('#id').val().trim();
                var lname = $('#lname').val().trim();
                var password = $('#password').val().trim();
                
                if (!id) {
                    CommonUtils.showToast('请输入管理员ID', 'error');
                    return;
                }
                if (!lname) {
                    CommonUtils.showToast('请输入账号', 'error');
                    return;
                }
                if (!password) {
                    CommonUtils.showToast('请输入登录密码', 'error');
                    return;
                }
                
                // 使用通用表单提交方法
                CommonUtils.submitForm('#adminForm', 
                    '/adminEdit', 
                    function(response) {
                        // 成功回调
                        CommonUtils.showToast('操作成功！', 'success');
                        setTimeout(function() {
                            window.location.href = '/adminList';
                        }, 1500);
                    },
                    function(error) {
                        // 错误回调
                        console.error('提交失败:', error);
                    }
                );
            });
        });
        </script>
        <script>
        $(document).ready(function() {
            // 页面加载时的初始化操作
            console.log('admin 页面加载完成');
        });
        </script>

        </div>
    </div>

    <!-- Bootstrap JS -->
</body>

</html>