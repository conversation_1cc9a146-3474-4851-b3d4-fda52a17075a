<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
<head th:replace="@{/admin/head.html}"></head>
  
</head>

<body>
    <div class="content-area" style="height: 100%; padding: 20px;">
        <h4 class="page-title">修改密码</h4>

        <div>
        <div class="container-fluid">
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">修改密码</h5>
        </div>
        <div class="card-body">
            <form id="passwordForm" method="post">
                <div class="form-group row mb-3">
                    <label for="txt_pwd" class="col-sm-3 col-form-label">原密码</label>
                    <div class="col-sm-9">
                        <input type="password" class="form-control" id="txt_pwd" name="txt_pwd" >
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="txt_pwd2" class="col-sm-3 col-form-label">新密码</label>
                    <div class="col-sm-9">
                        <input type="password" class="form-control" id="txt_pwd2" name="txt_pwd2" >
                    </div>
                </div>
                <div class="form-group row mb-3">
                    <label for="txt_pwd3" class="col-sm-3 col-form-label">确认密码</label>
                    <div class="col-sm-9">
                        <input type="password" class="form-control" id="txt_pwd3" name="txt_pwd3" >
                    </div>
                </div>
                <div class="form-group row">
                    <div class="col-sm-9 offset-sm-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-floppy-fill"></i> 保存
                        </button>
                        <a href="javascript:history.back()" class="btn btn-secondary ml-2">
                            <i class="bi bi-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
        <script>
        $(document).ready(function() {
            $('#passwordForm').on('submit', function(e) {
                e.preventDefault();
                
                // 非空验证
                var pwd1 = $('#txt_pwd').val().trim();
                var pwd2 = $('#txt_pwd2').val().trim();
                var pwd3 = $('#txt_pwd3').val().trim();
                
                if (!pwd1) {
                    CommonUtils.showToast('请输入原密码', 'error');
                    return;
                }
                if (!pwd2) {
                    CommonUtils.showToast('请输入新密码', 'error');
                    return;
                }
                if (!pwd3) {
                    CommonUtils.showToast('请输入确认密码', 'error');
                    return;
                }
                
                // 使用通用表单提交方法
                CommonUtils.submitForm('#passwordForm', 
                    '/adminPass', 
                    function(response) {
                        // 成功回调
                        CommonUtils.showToast('密码修改成功！', 'success');
                        setTimeout(function() {
                            // 清空表单
                            $('#passwordForm')[0].reset();
                        }, 1500);
                    },
                    function(error) {
                             console.error('提交失败:', error);
                    }
                );
            });
        });
        </script>

        </div>
    </div>

    <!-- Bootstrap JS -->
</body>

</html>