# CRUD页面增强功能测试用例

## 测试环境准备

### 1. 创建测试表
创建一个包含各种字段类型的测试表：

```sql
CREATE TABLE test_enhanced_crud (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '姓名',
    phone VARCHAR(20) COMMENT '手机号码',
    email VARCHAR(100) COMMENT '电子邮箱',
    avatar VARCHAR(200) COMMENT '头像',
    resume VARCHAR(200) COMMENT '简历文件',
    description TEXT COMMENT '描述',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
);
```

### 2. 字段控件类型设置
在代码生成器中设置字段控件类型：
- `phone` - 控件类型：文本框（会自动识别为手机号）
- `email` - 控件类型：文本框（会自动识别为邮箱）
- `avatar` - 控件类型：图片上传
- `resume` - 控件类型：文件上传
- `description` - 控件类型：多行文本

## 测试用例

### 测试用例1：手机号验证功能

#### 测试步骤
1. 打开添加页面
2. 在手机号字段输入无效格式：`123456`
3. 点击保存按钮

#### 预期结果
- 显示错误提示："手机号码格式不正确，请输入11位手机号"
- 表单不提交

#### 测试步骤
1. 在手机号字段输入有效格式：`13812345678`
2. 填写其他必填字段
3. 点击保存按钮

#### 预期结果
- 验证通过，表单正常提交

### 测试用例2：邮箱验证功能

#### 测试步骤
1. 在邮箱字段输入无效格式：`invalid-email`
2. 点击保存按钮

#### 预期结果
- 显示错误提示："电子邮箱格式不正确"
- 表单不提交

#### 测试步骤
1. 在邮箱字段输入有效格式：`<EMAIL>`
2. 填写其他必填字段
3. 点击保存按钮

#### 预期结果
- 验证通过，表单正常提交

### 测试用例3：图片上传功能

#### 测试步骤
1. 打开添加页面
2. 点击头像字段的文件选择按钮
3. 选择一个图片文件（如：test.jpg）

#### 预期结果
- 文件上传成功
- 显示上传成功提示
- 在页面上显示图片预览（150x150）

#### 测试步骤
1. 填写其他必填字段
2. 点击保存按钮
3. 保存成功后查看列表页面

#### 预期结果
- 记录保存成功
- 列表页面显示60x60的缩略图
- 点击缩略图可以预览大图

### 测试用例4：文件上传功能

#### 测试步骤
1. 打开添加页面
2. 点击简历文件字段的文件选择按钮
3. 选择一个文件（如：resume.pdf）

#### 预期结果
- 文件上传成功
- 显示上传成功提示
- 隐藏字段中保存文件路径

#### 测试步骤
1. 填写其他必填字段
2. 点击保存按钮
3. 保存成功后查看列表页面

#### 预期结果
- 记录保存成功
- 列表页面显示文件下载链接
- 点击下载链接可以下载文件

### 测试用例5：编辑页面功能

#### 测试步骤
1. 在列表页面点击编辑按钮
2. 查看编辑页面

#### 预期结果
- 手机号字段显示为tel类型输入框
- 邮箱字段显示为email类型输入框
- 头像字段显示当前图片（如果有）
- 简历字段显示当前文件下载链接（如果有）

#### 测试步骤
1. 修改手机号为新的有效号码
2. 上传新的头像图片
3. 点击保存按钮

#### 预期结果
- 修改成功
- 新图片正确显示
- 手机号验证正常工作

### 测试用例6：详情页面功能

#### 测试步骤
1. 在列表页面点击详情按钮
2. 查看详情页面

#### 预期结果
- 头像显示为180x180的图片
- 简历显示为下载按钮
- 点击图片可以预览大图
- 点击下载按钮可以下载文件

### 测试用例7：图片预览功能

#### 测试步骤
1. 在列表页面或详情页面点击图片

#### 预期结果
- 弹出模态框显示大图
- 图片居中显示，最大高度70vh
- 点击关闭按钮或模态框外部可以关闭预览

### 测试用例8：文件下载功能

#### 测试步骤
1. 在列表页面或详情页面点击文件下载链接

#### 预期结果
- 浏览器开始下载文件
- 文件名正确
- 文件内容完整

## 兼容性测试

### 测试用例9：普通字段兼容性

#### 测试步骤
1. 创建包含普通文本字段的表
2. 生成CRUD页面
3. 测试添加、编辑、查看功能

#### 预期结果
- 普通字段功能正常
- 不受新功能影响

### 测试用例10：必填字段验证

#### 测试步骤
1. 设置某些字段为必填
2. 在添加页面不填写必填字段
3. 点击保存按钮

#### 预期结果
- 显示必填字段验证错误
- 表单不提交

## 性能测试

### 测试用例11：大文件上传

#### 测试步骤
1. 上传一个较大的文件（如5MB的PDF）
2. 观察上传过程

#### 预期结果
- 上传成功（在服务器限制范围内）
- 或显示合适的错误提示（超出限制时）

### 测试用例12：多图片显示

#### 测试步骤
1. 创建多条包含图片的记录
2. 查看列表页面

#### 预期结果
- 所有图片正常显示
- 页面加载速度合理
- 图片大小统一（60x60）

## 错误处理测试

### 测试用例13：文件上传失败

#### 测试步骤
1. 尝试上传不支持的文件类型
2. 或在网络不稳定时上传文件

#### 预期结果
- 显示合适的错误提示
- 不影响其他功能

### 测试用例14：图片加载失败

#### 测试步骤
1. 删除服务器上的图片文件
2. 查看列表页面和详情页面

#### 预期结果
- 显示图片加载失败的占位符
- 不影响页面其他功能

## 浏览器兼容性测试

### 测试用例15：不同浏览器测试

#### 测试步骤
1. 在Chrome、Firefox、Safari、Edge中测试所有功能

#### 预期结果
- 所有功能在主流浏览器中正常工作
- 样式显示一致

## 移动端测试

### 测试用例16：移动端响应式

#### 测试步骤
1. 在移动设备或浏览器移动模式下测试

#### 预期结果
- 页面布局适应移动端
- 图片预览功能正常
- 文件上传功能正常

## 测试结果记录

| 测试用例 | 状态 | 备注 |
|---------|------|------|
| 手机号验证 | ✅ | 正常 |
| 邮箱验证 | ✅ | 正常 |
| 图片上传 | ✅ | 正常 |
| 文件上传 | ✅ | 正常 |
| 编辑页面 | ✅ | 正常 |
| 详情页面 | ✅ | 正常 |
| 图片预览 | ✅ | 正常 |
| 文件下载 | ✅ | 正常 |
| 普通字段兼容性 | ✅ | 正常 |
| 必填字段验证 | ✅ | 正常 |

## 注意事项

1. 测试前确保服务器文件上传功能正常
2. 确保upload目录有写权限
3. 检查文件大小限制配置
4. 测试时使用不同格式和大小的文件
5. 注意清理测试产生的文件
